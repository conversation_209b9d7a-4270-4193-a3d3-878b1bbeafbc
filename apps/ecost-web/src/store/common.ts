import { ref } from 'vue';
import { defineStore } from 'pinia';
import { getOrganizationTree } from '#/api/systemManagementApi/organizationApis';
import { getProductList } from '#/api/systemManagementApi/productApis';
import { roleTree } from '#/api/systemManagementApi/role_per';

export const useCommonStore = defineStore('common', () => {
  const organizationTree = ref([]);
  const roleTreeData = ref([]);
  const productData = ref([]);
  function getOrganizationTreeData() {
    const params = {};
    getOrganizationTree(params).then((res) => {
      organizationTree.value = res;
    });
  }

  function getRoleTreeData() {
    const productCode = sessionStorage.getItem('productCode') || null;
    if (productCode) {
      roleTree(productCode).then((res) => {
        roleTreeData.value = res;
      });
    }
  }

  async function getProductData() {
    const params = {};
    const product = await getProductList(params);
    productData.value = product;
  }

  function $reset() {
    organizationTree.value = [];
    roleTreeData.value = [];
    productData.value = [];
  }

  return {
    organizationTree,
    productData,
    roleTreeData,
    getOrganizationTreeData,
    getRoleTreeData,
    getProductData,
    $reset,
  };
});
