import { ecostServiceRequestClient } from '#/api/request';

/**
 * 机械字典 -- 获取版本
 */
export function ListMachineryDicVersion() {
  return ecostServiceRequestClient.get('/machinery-dictionary-version');
}

/**
 * 机械字典 -- 新增版本
 * @param data 版本数据
 */
export function AddMachineryDicVersion(data: any) {
  return ecostServiceRequestClient.post('/machinery-dictionary-version', data);
}

/**
 * 机械字典 -- 更新版本
 * @param versionId 版本ID
 * @param data 版本数据
 */
export function UpdateMachineryDicVersion(versionId: string, data: any) {
  return ecostServiceRequestClient.patch(
    `/machinery-dictionary-version/${versionId}`,
    data,
  );
}

/**
 * 机械字典 -- 删除版本
 * @param versionId 版本ID
 */
export function DelMachineryDicVersion(versionId: string) {
  return ecostServiceRequestClient.delete(
    `/machinery-dictionary-version/${versionId}`,
  );
}

/**
 * 机械字典 -- 获取分类列表
 * @param versionId 版本ID
 */
export function ListMachineryDicCategory(versionId: string) {
  return ecostServiceRequestClient.get(`/machinery-dictionary-category`, {
    params: {
      versionId,
    },
  });
}

/**
 * 机械字典 -- 创建分类
 * @param data 数据对象
 */
export function AddMachineryDicCategory(data: any) {
  return ecostServiceRequestClient.post(`/machinery-dictionary-category`, data);
}

/**
 * 机械字典 -- 更新分类
 * @param categoryId 分类ID
 * @param data 数据对象
 */
export function UpdateMachineryDicCategory(
  categoryId: string,
  data: any,
) {
  return ecostServiceRequestClient.patch(
    `/machinery-dictionary-category/${categoryId}`,
    data,
  );
}

/**
 * 机械字典 -- 删除分类
 * @param categoryId 分类ID
 */
export function DeleteMachineryDicCategory(categoryId: string) {
  return ecostServiceRequestClient.delete(
    `/machinery-dictionary-category/${categoryId}`,
  );
}

/**
 * 机械字典 -- 移动分类
 * @param categoryId 分类ID
 * @param direction 移动方向 - 'up' or 'down'
 */
export function MoveMachineryDicCategory(
  categoryId: string,
  direction: string,
) {
  return ecostServiceRequestClient.patch(
    `/machinery-dictionary-category/${categoryId}/_move?moveTo=${direction}`
  );
}

/**
 * 机械字典 -- 获取明细列表
 * @param params 查询参数
 */
export function ListMachineryDicDetail(params: any) {
  return ecostServiceRequestClient.get(`/machinery-dictionary-detail`, {
    params,
  });
}

/**
 * 机械字典 -- 搜索明细列表
 * @param params 查询参数
 */
export function QueryMachineryDicDetail(params: any) {
  return ecostServiceRequestClient.get(`/machinery-dictionary-detail/_search`, {
    params,
  });
}

/**
 * 机械字典 -- 搜索明细列表
 * @param taxrateDictionaryDetailId 查询参数
 */
export function QueryMachineryDicChangeLog(taxrateDictionaryDetailId: string) {
  return ecostServiceRequestClient.get(`/machinery-dictionary-detail/record/${taxrateDictionaryDetailId}`);
}

/**
 * 机械字典 -- 创建明细
 * @param data 数据对象
 */
export function AddMachineryDicDetail(data: any) {
  return ecostServiceRequestClient.post(`/machinery-dictionary-detail`, data);
}

/**
 * 机械字典 -- 更新明细
 * @param detailId 明细ID
 * @param data 数据对象
 */
export function UpdateMachineryDicDetail(detailId: string, data: any) {
  return ecostServiceRequestClient.patch(
    `/machinery-dictionary-detail/${detailId}`,
    data,
  );
}

/**
 * 机械字典 -- 删除明细
 * @param detailId 明细ID
 */
export function DeleteMachineryDicDetail(detailId: string) {
  return ecostServiceRequestClient.delete(
    `/machinery-dictionary-detail/${detailId}`,
  );
}

/**
 * 机械字典 -- 移动明细
 * @param detailId 明细ID
 * @param direction 移动方向 - 'up' or 'down'
 */
export function MoveMachineryDicDetail(
  detailId: string,
  direction: string,
) {
  return ecostServiceRequestClient.patch(
    `/machinery-dictionary-detail/${detailId}`,
    {},
    { moveTo: direction },
  );
}
