import { ecostServiceRequestClient } from '#/api/request';

/**
 * 获取分包标准清单
 * @param name
 */
export function getSubcontractingList(name?: string) {
  return ecostServiceRequestClient.get('/subcontracting-standards', {
    params: {
      name,
    },
  });
}

/**
 * 根据id查询分包标准清单详情
 * @param id
 */
export function getSubcontractingById(id: string) {
  return ecostServiceRequestClient.get(`/subcontracting-standards/${id}`);
}

/**
 * 新增分包标准清单版本
 * @param data
 */
export function addSubcontracting(data: any) {
  return ecostServiceRequestClient.post('/subcontracting-standards', data);
}

/**
 * 更新分包标准清单版本信息
 * @param id
 * @param data
 */
export function updateSubcontracting(id: string, data: any) {
  return ecostServiceRequestClient.patch(
    `/subcontracting-standards/${id}`,
    data,
  );
}

/**
 * 根据 版本id 删除 分包清单版本
 * @param id
 */
export function deleteSubcontracting(id: string) {
  return ecostServiceRequestClient.delete(`/subcontracting-standards/${id}`);
}

/**
 * 新增版本明细
 * @param SubcontractingId - 版本 id
 * @param data - 数据对象
 */
export function addSubcontractingDetails(SubcontractingId: string, data: any) {
  return ecostServiceRequestClient.post(
    `/subcontracting-standards/${SubcontractingId}/detail`,
    data,
  );
}

/**
 * 根据版本id获取明细列表
 * @param SubcontractingId
 */
export function getSubcontractingDetailsList(SubcontractingId: string) {
  return ecostServiceRequestClient.get(
    `/subcontracting-standards/detail?subcontractingStandardsId=${SubcontractingId}`,
  );
}

/**
 * 根据 明细id 查询单条明细详情
 * @param id - 明细id
 */
export function getSubcontractingDetailsInfo(id: string) {
  return ecostServiceRequestClient.get(
    `/subcontracting-standards/detail/${id}`,
  );
}

/**
 * 根据明细 id 更新明细信息
 * @param id 明细 id
 * @param data 数据对象
 */
export function upDateSubcontractingDetails(id: string, data: any) {
  return ecostServiceRequestClient.patch(
    `/subcontracting-standards/detail/${id}`,
    data,
  );
}

/**
 * 根据 明细id 删除明细数据
 * @param id - 明细id
 */
export function deleteDetails(id: string) {
  return ecostServiceRequestClient.delete(
    `/subcontracting-standards/detail/${id}`,
  );
}

/**
 * 根据明细id启用本级及其子级数据
 * @param ids
 */
export function enable(ids: Array<string>) {
  return ecostServiceRequestClient.patch(
    `/subcontracting-standards/detail/enable`,
    {
      ids,
    },
  );
}

/**
 * 根据明细id禁用本级及其子级数据
 * @param ids
 */
export function discard(ids: Array<string>) {
  return ecostServiceRequestClient.patch(
    `/subcontracting-standards/detail/discard`,
    {
      ids,
    },
  );
}
