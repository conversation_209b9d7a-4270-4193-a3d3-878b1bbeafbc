import { ecostServiceRequestClient } from '#/api/request';

/**
 * 费用字典 -- 获取版本
 */
export function ListCostDicVersion() {
  return ecostServiceRequestClient.get('/cost-dictionary-version');
}

/**
 * 费用字典 -- 新增版本
 * @param data 版本数据
 */
export function AddCostDicVersion(data: any) {
  return ecostServiceRequestClient.post('/cost-dictionary-version', data);
}

/**
 * 费用字典 -- 更新版本
 * @param versionId 版本ID
 * @param data 版本数据
 */
export function UpdateCostDicVersion(versionId: string, data: any) {
  return ecostServiceRequestClient.patch(
    `/cost-dictionary-version/${versionId}`,
    data,
  );
}

/**
 * 费用字典 -- 删除版本
 * @param versionId 版本ID
 */
export function DelCostDicVersion(versionId: string) {
  return ecostServiceRequestClient.delete(
    `/cost-dictionary-version/${versionId}`,
  );
}

/**
 * 费用字典 -- 获取分类列表
 * @param versionId 版本ID
 */
export function ListCostDicCategory(versionId: string) {
  return ecostServiceRequestClient.get(`/cost-dictionary-category`, {
    params: {
      versionId,
    },
  });
}

/**
 * 费用字典 -- 创建分类
 * @param data 数据对象
 */
export function AddCostDicCategory(data: any) {
  return ecostServiceRequestClient.post(`/cost-dictionary-category`, data);
}

/**
 * 费用字典 -- 更新分类
 * @param categoryId 分类ID
 * @param data 数据对象
 */
export function UpdateCostDicCategory(
  categoryId: string,
  data: any,
) {
  return ecostServiceRequestClient.patch(
    `/cost-dictionary-category/${categoryId}`,
    data,
  );
}

/**
 * 费用字典 -- 删除分类
 * @param categoryId 分类ID
 */
export function DeleteCostDicCategory(categoryId: string) {
  return ecostServiceRequestClient.delete(
    `/cost-dictionary-category/${categoryId}`,
  );
}

/**
 * 费用字典 -- 移动分类
 * @param categoryId 分类ID
 * @param direction 移动方向 - 'up' or 'down'
 */
export function MoveCostDicCategory(
  categoryId: string,
  direction: string,
) {
  return ecostServiceRequestClient.patch(
    `/cost-dictionary-category/${categoryId}/_move?moveTo=${direction}`
  );
}

/**
 * 费用字典 -- 获取明细列表
 * @param params 查询参数
 */
export function ListCostDicDetail(params: any) {
  return ecostServiceRequestClient.get(`/cost-dictionary-detail`, {
    params,
  });
}

/**
 * 费用字典 -- 搜索明细列表
 * @param params 查询参数
 */
export function QueryCostDicDetail(params: any) {
  return ecostServiceRequestClient.get(`/cost-dictionary-detail/_search`, {
    params,
  });
}

/**
 * 费用字典 -- 搜索明细列表
 * @param taxrateDictionaryDetailId 查询参数
 */
export function QueryCostDicChangeLog(taxrateDictionaryDetailId: string) {
  return ecostServiceRequestClient.get(`/cost-dictionary-detail/record/${taxrateDictionaryDetailId}`);
}

/**
 * 费用字典 -- 创建明细
 * @param data 数据对象
 */
export function AddCostDicDetail(data: any) {
  return ecostServiceRequestClient.post(`/cost-dictionary-detail`, data);
}

/**
 * 费用字典 -- 更新明细
 * @param detailId 明细ID
 * @param data 数据对象
 */
export function UpdateCostDicDetail(detailId: string, data: any) {
  return ecostServiceRequestClient.patch(
    `/cost-dictionary-detail/${detailId}`,
    data,
  );
}

/**
 * 费用字典 -- 删除明细
 * @param detailId 明细ID
 */
export function DeleteCostDicDetail(detailId: string) {
  return ecostServiceRequestClient.delete(
    `/cost-dictionary-detail/${detailId}`,
  );
}

/**
 * 费用字典 -- 移动明细
 * @param detailId 明细ID
 * @param direction 移动方向 - 'up' or 'down'
 */
export function MoveCostDicDetail(
  detailId: string,
  direction: string,
) {
  return ecostServiceRequestClient.patch(
    `/cost-dictionary-detail/${detailId}`,
    {},
    { moveTo: direction },
  );
}
