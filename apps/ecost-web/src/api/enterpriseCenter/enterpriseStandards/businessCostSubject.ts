import { ecostServiceRequestClient } from '#/api/request';

/**
 * 业务成本科目 -- 获取版本
 * @param status 状态
 */
export function ListBusinessCostVersion(status?: string) {
  return ecostServiceRequestClient.get('/business-cost-subject-version', {
    params: {
      status,
    },
  });
}

/**
 * 业务成本科目 -- 新增版本
 */
export function AddBusinessCostVersion(data: any) {
  return ecostServiceRequestClient.post('/business-cost-subject-version', data);
}

/**
 * 业务成本科目 -- 修改版本
 * @param id 版本ID
 * @param data 版本数据
 */
export function UpdateBusinessCostVersion(id: string, data: any) {
  return ecostServiceRequestClient.patch(
    `business-cost-subject-version/${id}`,
    data,
  );
}

/**
 * 业务成本科目 -- 删除版本
 * @param id 版本ID
 */
export function DelBusinessCostVersion(id: string) {
  return ecostServiceRequestClient.delete(
    `/business-cost-subject-version/${id}`,
  );
}

/**
 * 业务成本科目 -- 根据版本获取分类列表
 * @param versionId 版本ID
 */
export function ListBusinessCostCategory(versionId: string) {
  return ecostServiceRequestClient.get(`/business-cost-subject-category`, {
    params: {
      versionId,
    },
  });
}

/**
 * 业务成本科目 -- 新增分类
 * @param data 分类数据
 */
export function AddBusinessCostCategory(data: any) {
  return ecostServiceRequestClient.post(
    `/business-cost-subject-category`,
    data,
  );
}

/**
 * 业务成本科目 -- 更新分类
 * @param categoryId 分类ID
 * @param data 分类数据
 */
export function UpdateBusinessCostCategory(categoryId: string, data: any) {
  return ecostServiceRequestClient.patch(
    `/business-cost-subject-category/${categoryId}`,
    data,
  );
}

/**
 * 业务成本科目 -- 删除分类
 * @param categoryId 分类ID
 */
export function DelBusinessCostCategory(categoryId: string) {
  return ecostServiceRequestClient.delete(
    `/business-cost-subject-category/${categoryId}`,
  );
}

/**
 * 业务成本科目 -- 移动分类
 * @param categoryId 分类ID
 * @param direction 移动方向
 */
export function MoveBusinessCostCategory(
  categoryId: string,
  direction: string,
) {
  return ecostServiceRequestClient.patch(
    `/business-cost-subject-category/${categoryId}/_move`,
    {},
    {
      moveTo: direction,
    },
  );
}

/**
 * 业务成本科目 -- 搜索明细列表
 * @param params 查询参数
 */
export function QueryBusinessCostDetail(params: any) {
  return ecostServiceRequestClient.get(
    `/business-cost-subject-detail/_search`,
    {
      params,
    },
  );
}

/**
 * 业务成本科目 -- 获取明细树（分类 + 明细）
 * @param versionId 明细id
 */
export function TreeBusinessCostCategoryDetails(versionId: string) {
  return ecostServiceRequestClient.get(`/business-cost-subject-detail/_tree`, {
    params: {
      businessCostSubjectVersionId: versionId,
    },
  });
}

/**
 * 业务成本科目 -- 获取明细列表
 * @param params 查询参数
 */
export function ListBusinessCostDetail(params: any) {
  return ecostServiceRequestClient.get(`/business-cost-subject-detail`, {
    params,
  });
}

/**
 * 业务成本科目 -- 创建明细
 * @param data 明细数据
 */
export function AddBusinessCostDetail(data: any) {
  return ecostServiceRequestClient.post(`/business-cost-subject-detail`, data);
}

/**
 * 业务成本科目 -- 更新明细
 * @param detailId 明细id
 * @param data 明细数据
 */
export function UpdateBusinessCostDetail(detailId: string, data: any) {
  return ecostServiceRequestClient.patch(
    `/business-cost-subject-detail/${detailId}`,
    data,
  );
}

/**
 * 业务成本科目 -- 删除明细
 * @param detailId 明细id
 */
export function DelBusinessCostDetail(detailId: string) {
  return ecostServiceRequestClient.delete(
    `/business-cost-subject-detail/${detailId}`,
  );
}

/**
 * 业务成本科目 -- 移动明细明细
 * @param detailId 明细id
 * @param direction 移动方向 up -上移 down - 下移
 */
export function MoveBusinessCostDetail(detailId: string, direction: string) {
  return ecostServiceRequestClient.patch(
    `/business-cost-subject-detail/${detailId}/_move`,
    {},
    {
      moveTo: direction,
    },
  );
}
