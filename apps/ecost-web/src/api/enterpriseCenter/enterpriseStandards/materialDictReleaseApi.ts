import { ecostServiceRequestClient } from '#/api/request';

/**
 * 创建材料版本
 * @param data
 * @returns
 */
export function addMaterial(data: any) {
  return ecostServiceRequestClient.post('/material-dictionary', data);
}

/**
 * 查询材料字典列表
 * @param name
 */
export function getMaterialList(name?: string) {
  return ecostServiceRequestClient.get(`/material-dictionary`, {
    params: {
      name,
    },
  });
}

/**
 * 根据 id 查询材料字典详情
 * @param id
 */
export function getMaterialById(id: string) {
  return ecostServiceRequestClient.get(`/material-dictionary/${id}`);
}

/**
 * 根据 id 删除材料字典版本
 * @param id
 */
export function deleteMaterial(id: string) {
  return ecostServiceRequestClient.delete(`/material-dictionary/${id}`);
}

/**
 * 废弃材料字典版本
 * @param list
 */
export function discardMaterialVersion(list: Array<string>) {
  return ecostServiceRequestClient.patch('/material-dictionary/discard', list);
}

/**
 * 启用材料字典版本
 * @param list
 */
export function enableMaterialVersion(list: Array<string>) {
  return ecostServiceRequestClient.patch('/material-dictionary/enable', list);
}
