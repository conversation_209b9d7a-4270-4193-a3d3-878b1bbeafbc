import { ecostServiceRequestClient } from '#/api/request';

/**
 * 获取版本列表
 */
export function versionList(versionType: string) {
  return ecostServiceRequestClient.get(`/version/list`, {
    params: {
      moduleType: versionType,
    },
  });
}

/**
 * 获取版本列表
 *  @id - 数据ID
 */
export function versionGetById(id: string) {
  return ecostServiceRequestClient.get(`/version/obj/${id}`);
}

/**
 * 新增版本
 * @param params
 */
export function versionAdd(params: any) {
  return ecostServiceRequestClient.post('/version/add', {
    params,
  });
}

/**
 * 修改版本
 *  @id - 数据ID
 *  @data
 */
export function versionUpdate(id: string, data: any) {
  return ecostServiceRequestClient.patch(`/version/edit/${id}`, data);
}

/**
 * 删除版本
 *  @id - 数据ID
 */
export function versionDel(id: string) {
  return ecostServiceRequestClient.delete(`/version/del/${id}`);
}
