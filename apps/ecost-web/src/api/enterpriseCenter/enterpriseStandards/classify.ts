import { ecostServiceRequestClient } from '#/api/request';

/**
 * 获取分类列表
 * @Params versionId - 版本ID
 */
export function classifyList(versionId: string) {
  return ecostServiceRequestClient.get(`/classify/list/${versionId}`);
}

/**
 * 获取分类列表
 * @Params id - 数据id
 */
export function classifyGetById(id: string) {
  return ecostServiceRequestClient.get(`/classify/obj/${id}`);
}

/**
 * 新增分类
 * @Params data
 */
export function classifyAdd(data: any) {
  return ecostServiceRequestClient.post(`/classify/add`, data);
}

/**
 * 编辑分类
 * @Params id - 数据id
 * @Params data
 */
export function classifyUpdate(id: string, data: any) {
  return ecostServiceRequestClient.patch(`/classify/edit/${id}`, data);
}

/**
 * 删除分类
 * @Params id - 数据id
 */
export function classifyDel(id: string) {
  return ecostServiceRequestClient.delete(`/classify/del/${id}`);
}
