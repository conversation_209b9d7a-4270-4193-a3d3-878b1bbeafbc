import { ecostServiceRequestClient } from '#/api/request';

/**
 * 项目基本字段设置 -- 获取分组
 */
export function ListProjectBasicField() {
  return ecostServiceRequestClient.get('/project-basic-info-category/list');
}

/**
 * 项目基本字段设置 -- 查询分组状态
 */
export function StatusProjectBasicField() {
  return ecostServiceRequestClient.get('/project-basic-info-category/status');
}

/**
 * 项目基本字段设置 -- 新增分组
 * @param data - 数据对象
 */
export function AddProjectBasicField(data: any) {
  return ecostServiceRequestClient.post(
    '/project-basic-info-category/add',
    data,
  );
}

/**
 * 项目基本字段设置 -- 更新分组
 * @param id - 分组id
 * @param data - 数据对象
 */
export function UpdateProjectBasicField(id: string, data: any) {
  return ecostServiceRequestClient.patch(
    `/project-basic-info-category/edit/${id}`,
    data,
  );
}

/**
 * 项目基本字段设置 -- 删除分组
 * @param id - 分组id
 */
export function DeleteProjectBasicField(id: string) {
  return ecostServiceRequestClient.delete(
    `/project-basic-info-category/del/${id}`,
  );
}

/**
 * 项目基本字段设置 -- 分组上移 & 下移
 * @param id - 分组id
 * @param moveType up:上移，down：下移
 */
export function MoveProjectBasicField(id: string, moveType: 'down' | 'up') {
  return ecostServiceRequestClient.post(
    `/project-basic-info-category/edit/move`,
    {
      id,
      moveType,
    },
  );
}

/**
 * 项目基本字段设置 -- 发布分组
 * @param publishStatus - 发布状态 发布:true,取消发布:false
 */
export function PublishProjectBasicField(publishStatus: boolean) {
  return ecostServiceRequestClient.patch(
    `/project-basic-info-category/publish`,
    {
      isPublish: publishStatus,
    },
  );
}

/**
 * 项目基本字段设置 -- 获取字段明细
 * @param basicProjectInfoCategoryId - 分组id
 */
export function ListProjectBasicFieldDetail(
  basicProjectInfoCategoryId: string,
) {
  return ecostServiceRequestClient.get(
    `/project-basic-info-field-detail/list/${basicProjectInfoCategoryId}`,
  );
}

/**
 * 项目基本字段设置 -- 新增字段明细
 * @param data - 字段明细数据对象
 */
export function AddProjectBasicFieldDetail(data: any) {
  return ecostServiceRequestClient.post(
    `/project-basic-info-field-detail/add`,
    data,
  );
}

/**
 * 项目基本字段设置 -- 编辑字段明细
 * @param id - 明细id
 * @param data - 字段明细数据对象
 */
export function UpdateProjectBasicFieldDetail(id: string, data: any) {
  return ecostServiceRequestClient.patch(
    `/project-basic-info-field-detail/edit/${id}`,
    data,
  );
}

/**
 * 项目基本字段设置 -- 删除字段明细
 * @param id - 明细id
 */
export function DeleteProjectBasicFieldDetail(id: string) {
  return ecostServiceRequestClient.delete(
    `/project-basic-info-field-detail/del/${id}`,
  );
}

/**
 * 项目基本字段设置 -- 修改字段启用状态
 * @param id - 明细id
 * @param status - 启用状态 true:启用，false:禁用
 */
export function EnableProjectBasicFieldDetail(id: string, status: boolean) {
  return ecostServiceRequestClient.patch(
    `/project-basic-info-field-detail/publish/${id}`,
    {
      status,
    },
  );
}

/**
 * 项目基本字段设置 -- 明细上下移
 * @param id - 明细id
 * @param moveType - up:上移，down：下移
 */
export function MoveProjectBasicFieldDetail(
  id: string,
  moveType: 'down' | 'up',
) {
  return ecostServiceRequestClient.post(
    `/project-basic-info-field-detail/edit/move`,
    {
      id,
      moveType,
    },
  );
}
