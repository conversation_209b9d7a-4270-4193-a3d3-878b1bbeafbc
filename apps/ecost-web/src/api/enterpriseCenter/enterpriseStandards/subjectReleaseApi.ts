import { ecostServiceRequestClient } from '#/api/request';

/**
 * 获取成本科目版本列表
 */
export function businessCostSubjectVersionList() {
  return ecostServiceRequestClient.get('/business-cost-account');
}

/**
 * 新增成本科目版本
 * @param data
 */
export function businessCostSubjectVersionAdd(data: any) {
  return ecostServiceRequestClient.post('/business-cost-account', data);
}

/**
 * 更新成本科目版本
 * @param id
 * @param data
 */
export function businessCostSubjectVersionUpdate(id: string, data: any) {
  return ecostServiceRequestClient.patch(`/business-cost-account/${id}`, data);
}

/**
 * 删除成本科目版本
 * @param id
 */
export function businessCostSubjectVersionUpdateDel(id: string) {
  return ecostServiceRequestClient.delete(`/business-cost-account/${id}`);
}

/**
 * 根据id查询成本科目版本详情
 * @param id
 */
export function getBusinessCostById(id: string) {
  return ecostServiceRequestClient.get(`/business-cost-account/${id}`);
}

/**
 * 新增版本明细
 * @param businessCostAccountId - 版本 id
 * @param data - 数据对象
 */
export function addBusinessCostDetails(
  businessCostAccountId: string,
  data: any,
) {
  return ecostServiceRequestClient.post(
    `/business-cost-account/${businessCostAccountId}/detail`,
    data,
  );
}

/**
 * 根据版本id获取明细列表
 * @param businessCostAccountId
 */
export function getBusinessCostDetailsList(businessCostAccountId: string) {
  return ecostServiceRequestClient.get(
    `/business-cost-account/detail?businessCostAccountId=${businessCostAccountId}`,
  );
}

/**
 * 根据 明细id 查询单条明细详情
 * @param id - 明细id
 */
export function getBusinessCostDetailsInfo(id: string) {
  return ecostServiceRequestClient.get(`/business-cost-account/detail/${id}`);
}

/**
 * 根据明细 id 更新明细信息
 * @param id 明细 id
 * @param data 数据对象
 */
export function upDateBusinessCostDetails(id: string, data: any) {
  return ecostServiceRequestClient.patch(
    `/business-cost-account/detail/${id}`,
    data,
  );
}

/**
 * 根据 明细id 删除明细数据
 * @param id - 明细id
 */
export function deleteDetails(id: string) {
  return ecostServiceRequestClient.delete(
    `/business-cost-account/detail/${id}`,
  );
}

/**
 * 获取财务成本信息
 */
export function getFinancialCostList(mode?: string) {
  return ecostServiceRequestClient.get(`financial-cost-account?mode=${mode}`);
}
