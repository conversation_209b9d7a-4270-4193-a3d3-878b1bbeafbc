import { ecostServiceRequestClient } from '#/api/request';

/**
 * 获取合同范本数据
 * @param params
 * @returns
 */
export function getContractStandardList(params: any) {
  return ecostServiceRequestClient.get(
    'enterprise-standard-contract-template/list',
    {
      params,
    },
  );
}

/**
 * 新增合同范本数据
 * @param {name,classify,remark,filePath}
 * @returns
 */
export function addContractStandard(data: any) {
  return ecostServiceRequestClient.post(
    'enterprise-standard-contract-template/add',
    data,
  );
}

/**
 * 获取合同范本分类对象
 * @id
 * @param {name,orgId,remark}
 * @returns
 */
export function editContractStandardObj(id: string) {
  return ecostServiceRequestClient.get(
    `enterprise-standard-contract-template/obj/${id}`,
  );
}

/**
 * 编辑合同范本
 * @id
 * @param {name,orgId,remark}
 * @returns
 */
export function editContractStandard(id: string, data: any) {
  return ecostServiceRequestClient.patch(
    `enterprise-standard-contract-template/edit/${id}`,
    data,
  );
}

/**
 * 修改合同范本状态
 * @param {id,versionStatus}
 * @returns
 */
export function editContractStandardStatus(data: any) {
  return ecostServiceRequestClient.post(
    `enterprise-standard-contract-template/edit/status`,
    data,
  );
}
/**
 * 移动合同范本数据
 * @param id
 * @returns
 */
interface moveContractStandard {
  id:string,
  moveType: 'up' | 'down'
}
export function moveContractStandard(data: moveContractStandard) {
  return ecostServiceRequestClient.post(
    `enterprise-standard-contract-template/edit/move`,
    data,
  );
}

/**
 * 删除合同范本数据
 * @param id
 * @returns
 */
export function delContractStandard(id: any) {
  return ecostServiceRequestClient.delete(
    `enterprise-standard-contract-template/delete/${id}`,
  );
}



/**
 * 查询所有字段规则
 * @param id
 * @returns
 */
type queryStandardFieldRule = {
  name?:string;
  contractTemplateId:string
}
export function getStandardFieldRuleList(params: queryStandardFieldRule) {
  return ecostServiceRequestClient.get(
    `enterprise-standard-field-rule/list`,
    {
      params,
    },
  );
}

/**
 * 合同范本新增字段规则
 * @param id
 * @returns
 */
type standardFieldRule = {
  coord: string,
  fieldRuleId: string,
  contractTemplateId: string
}
export function addContractStandardFieldRule(data: standardFieldRule) {
  return ecostServiceRequestClient.post(
    `enterprise-standard-field-rule/add`,
    data,
  );
}


/**
 * 合同范本取消字段规则
 * @param id
 * @returns
 */
export function delContractStandardFieldRule(data: standardFieldRule) {
  return ecostServiceRequestClient.post(
    `enterprise-standard-field-rule/del`,
    data,
  );
}



/**
 * 获取强制条款列表
 * @param params
 * @returns
 */
type mandatoryTermList = {
  contractTemplateId:string
}
export function getMandatoryTermList(params: mandatoryTermList) {
  return ecostServiceRequestClient.get(
    `/enterprise-standard-mandatory-term/list`,
    {params},
  );
}

/**
 * 新增强制条款
 * @param data
 * @returns
 */
type mandatoryTerm = {
  name: string;
  content: string;
  contractTemplateId: string
}
export function addMandatoryTerm(data: mandatoryTerm) {
  return ecostServiceRequestClient.post(
    `/enterprise-standard-mandatory-term/add`,
    data,
  );
}

/**
 * 删除强制条款
 * @param id
 * @returns
 */

export function delMandatoryTerm(id: string) {
  return ecostServiceRequestClient.delete(
    `/enterprise-standard-mandatory-term/del/${id}`,
  );
}