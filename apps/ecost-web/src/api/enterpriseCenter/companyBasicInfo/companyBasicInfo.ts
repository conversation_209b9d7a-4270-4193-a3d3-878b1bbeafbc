import { ecostServiceRequestClient } from '#/api/request';

/**
 * 新增公司基本信息版本
 * @param {versionName}
 * @returns
 */
export function addBusinessVersion(data: any) {
  return ecostServiceRequestClient.post(
    'business-base-info/add',
    data,
  );
}

/**
 * 修改公司基本信息
 * @param id
 * @returns
 */
export function updateBusinessVersion(id: string,data:any) {
   return ecostServiceRequestClient.post(
    `business-base-info/edit/${id}`,
    data,
  );
}
/**
 * 保存公司基本信息
 * @param
 * @returns
 */
export function saveBusinessInfo(id:string,data: any) {
  return ecostServiceRequestClient.patch(
    `business-base-info/save/${id}`,
    data,
  );
}

/**
 * 发布公司基本信息
 * @param 
 * @returns
 */
export function publishBusinessInfo(id:string) {
  return ecostServiceRequestClient.patch(
    `business-base-info/publish/${id}`
  );
}

/**
 * 取消发布公司基本信息
 * @param 
 * @returns
 */
export function cancelPublishBusinessInfo(id:string) {
  return ecostServiceRequestClient.patch(
    `business-base-info/cancel-publish/${id}`
  );
}


/**
 * 获取公司基本信息数据
 * @param params
 * @returns
 */
export function getBusinessInfoList() {
  return ecostServiceRequestClient.get(
    'business-base-info/list'
  );
}


/**
 * 获取公司基本信息变更记录数据
 * @param params
 * @returns
 */
export function getBusinessInfoChangeLogList(id:string) {
  return ecostServiceRequestClient.get(
    `business-base-info/change-log-list/${id}`
  );
}


/**
 * 删除移动公司基本信息
 * @param id
 * @returns
 */
export function delBusinessInfo(id: string) {
  return ecostServiceRequestClient.delete(
    `business-base-info/delete/${id}`,
  );
}
/**
 * 移动公司基本信息
 * @param id
 * @returns
 */
interface moveBusinessInfo {
  id:string,
  moveType: 'up' | 'down'
}
export function moveBusinessInfo(data: moveBusinessInfo) {
  return ecostServiceRequestClient.post(
    `business-base-info/edit/move`,
    data,
  );
}