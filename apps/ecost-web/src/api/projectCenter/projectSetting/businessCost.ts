import { ecostServiceRequestClient } from '#/api/request';

/**
 * 项目基本信息 - 获取所有配置信息
 */
export function DictList() {
  return ecostServiceRequestClient.get('/business-cost-account/list');
}

/**
 * 项目基本信息 - 获取版本列表 - 弹框内
 * @param accountDictionaryId - 字典id
 */
export function VersionList(accountDictionaryId: string) {
  return ecostServiceRequestClient.get('/business-cost-account/version/list', {
    params: {
      accountDictionaryId,
    },
  });
}

/**
 * 项目基本信息 - 获取分类列表 - 弹框内
 * @param accountDictionaryId - 字典id
 * @param versionId - 版本id
 */
export function CategoryList(accountDictionaryId: string, versionId: string) {
  return ecostServiceRequestClient.get('/business-cost-account/category/list', {
    params: {
      accountDictionaryId,
      versionId,
    },
  });
}

/**
 * 项目基本信息 - 获取明细列表 - 弹框内
 * @param accountDictionaryId - 字典id
 * @param versionId - 版本id
 * @param categoryId - 分类id
 */
export function DetailsList(
  accountDictionaryId: string,
  versionId: string,
  categoryId: string,
) {
  return ecostServiceRequestClient.get('/business-cost-account/detail/list', {
    params: {
      accountDictionaryId,
      versionId,
      categoryId,
    },
  });
}

/**
 * 项目基本信息 - 获取已选择的版本分类列表 - 弹框外
 * @param accountDictionaryId - 字典id
 */
export function SelectVersionList(accountDictionaryId: string) {
  return ecostServiceRequestClient.get(
    '/business-cost-account/choose/version/list',
    {
      params: {
        accountDictionaryId,
      },
    },
  );
}

/**
 * 项目基本信息 - 获取已选择的版本分类列表 - 弹框外
 * @param accountDictionaryId - 字典id
 * @param versionId - 版本id
 * @param categoryId - 分类id
 */
export function SelectDetailsList(
  accountDictionaryId: string,
  versionId: string,
  categoryId: string,
) {
  return ecostServiceRequestClient.get(
    '/business-cost-account/choose/detail/list',
    {
      params: {
        accountDictionaryId,
        versionId,
        categoryId,
      },
    },
  );
}

/**
 * 项目基本信息 - 删除版本分类列表
 * @param versionId - 数据id
 * @param accountDictionaryId - 字典id
 */
export function DeleteSelectData(
  versionId: string,
  accountDictionaryId: string,
) {
  return ecostServiceRequestClient.delete(
    `/business-cost-account/version/delete/${versionId}`,
    {
      params: {
        accountDictionaryId,
      },
    },
  );
}

/**
 * 项目基本信息 - 删除分类数据
 * @param versionId - 数据id
 * @Param categoryId - 分类id
 * @param accountDictionaryId - 分类id
 */
export function DeleteSelectCategoryData(
  versionId: string,
  categoryId: string,
  accountDictionaryId: string,
) {
  return ecostServiceRequestClient.delete(
    `/business-cost-account/category/delete/${categoryId}`,
    {
      params: {
        accountDictionaryId,
        versionId,
      },
    },
  );
}

/**
 * 项目基本信息 - 删除分类数据
 * @param versionId - 数据id
 * @Param categoryId - 分类id
 * @param detailId - 明细id
 * @param accountDictionaryId - 分类id
 */
export function DeleteSelectDetailData(
  versionId: string,
  categoryId: string,
  detailId: string,
  accountDictionaryId: string,
) {
  return ecostServiceRequestClient.delete(
    `/business-cost-account/detail/delete/${detailId}`,
    {
      params: {
        accountDictionaryId,
        versionId,
        categoryId,
      },
    },
  );
}

/**
 * 项目基本信息 - 业务成本科目字典保存
 * @param data - 数据对象
 */
export function AddSelectBusinessCostAccountData(data: any) {
  return ecostServiceRequestClient.post(
    `/business-cost-account/cost/add`,
    data,
  );
}

/**
 * 项目基本信息 - 其他业务成本科目保存
 * @param data - 数据对象
 */
export function AddSelectAccountData(data: any) {
  return ecostServiceRequestClient.post(
    `/business-cost-account/other/add`,
    data,
  );
}
