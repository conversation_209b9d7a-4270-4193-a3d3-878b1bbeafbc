import { ecostServiceRequestClient } from '#/api/request';

/**
 * 上传
 * @param {file}
 * @returns
 */
export function fileUpload(file: File,fileName?:string) {
  const formData = new FormData();
  formData.append('file', file);
  const url = fileName ? `file-manage/upload/${fileName}` :`file-manage/upload/`
  return ecostServiceRequestClient.post(
    url,
    formData,
    {
      headers: {
        'Content-Type': 'multipart/form-data', // 设置为文件上传类型
      },
    },
  );
}

/**
 * 下载
 * @fileName
 * @returns
 */
export function fileDownload(fileName: string) {
  return ecostServiceRequestClient.post(`file-manage/down/${fileName}`  ,{}, {
    responseType: 'arraybuffer',
  });
}
