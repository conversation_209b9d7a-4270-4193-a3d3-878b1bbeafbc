import { metaServiceRequestClient } from '#/api/request';

/**
 * 获取角色列表
 * @param params
 */
function getRoleList(params: any) {
  return metaServiceRequestClient.get('/role', { params });
}

/**
 * 根据id获取角色详情
 * @param id
 */
function getRoleById(id: number | string) {
  return metaServiceRequestClient.get(`/role/${id}`);
}

/**
 * 更新角色信息
 * @param id
 * @param data
 */
function updateRole(id: number, data: any) {
  return metaServiceRequestClient.patch(`/role/${id}`, data);
}

/**
 * 删除角色
 * @param id
 */
function deleteRole(id: number) {
  return metaServiceRequestClient.delete(`/role/${id}`);
}

/**
 * 新增角色
 * @param data
 * @returns
 */

function addRole(data: any) {
  return metaServiceRequestClient.post('/role', data);
}

export { getRoleList, getRoleById, updateRole, deleteRole, addRole };
