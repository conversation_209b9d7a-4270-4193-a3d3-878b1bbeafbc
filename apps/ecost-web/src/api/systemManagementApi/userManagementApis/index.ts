import { metaServiceRequestClient } from '#/api/request';

/**
 * 获取用户列表
 * @param params
 */
export function getNotOrgUserList(params: any) {
  return metaServiceRequestClient.get('/user/filter-users-org', { params });
}

/**
 * 获取非当前组织的用户列表
 * @param params
 */
export function getUserList(params: any) {
  return metaServiceRequestClient.get('/user/list', { params });
}

/**
 * 根据id获取用户信息
 * @param id
 */
export function getUserById(id: string) {
  return metaServiceRequestClient.get(`/user/${id}`);
}

/**
 * 新增用户
 * @param id
 * @param data
 */
export function createUser(data: any) {
  return metaServiceRequestClient.post(`/user/add`, data);
}

/**
 * 更新用户信息
 * @param id
 * @param data
 */
export function updateUser(id: string, data: any) {
  return metaServiceRequestClient.patch(`/user/update/${id}`, data);
}

/**
 * 更新用户状态
 * @param id
 * @param data
 */
export function boolUser(id: string, data: any) {
  return metaServiceRequestClient.patch(`/user/bool/${id}`, data);
}

/**
 * 分配用户组织
 * @param id
 * @param data
 */
export function updateUserOrg(id: string, data: any) {
  return metaServiceRequestClient.post(`/user/${id}/orgs`, data);
}

/**
 * 分配用户角色
 * @param id
 * @param data
 */
export function updateUserRole(id: string, data: any) {
  return metaServiceRequestClient.post(`/user/${id}/roles`, data);
}

/**
 * 删除用户
 * @param id
 */
export function deleteUser(id: string) {
  return metaServiceRequestClient.delete(`/user/del/${id}`);
}

/**
 * 导出用户
 */
export function exportUser(params: any) {
  return metaServiceRequestClient.post(`/user/export-user`, params, {
    responseType: 'blob',
  });
}
/**
 * 导入用户
 */
export function importUser(file: File,orgId:string) {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('orgId', orgId);

  return metaServiceRequestClient.post(`/user/import-user`, formData, {
    headers: {
      'Content-Type': 'multipart/form-data', // 设置为文件上传类型
    },
  });
}

/**
 * 获取用户角色列表
 * @param id
 */
export function getUserRolesList(id: string) {
  return metaServiceRequestClient.get(`/user/${id}/roles`);
}

export default {
  createUser,
  deleteUser,
  getUserById,
  getUserList,
  getNotOrgUserList,
  updateUser,
  boolUser,
  getUserRolesList,
  updateUserOrg,
  updateUserRole,
  exportUser,
  importUser,
};
