<template>
  <div class="flex items-center gap-2">
    <div class="font-size">展开到</div>
    <ElTooltip
      v-for="(item, idx) in expandBtnList"
      :key="idx"
      :content="item.title"
      placement="top"
    >
      <IconifyIcon
        :class="`icon-btn ${props.expandIdx === item.id ? 'active' : ''}`"
        :icon="item.iconName"
        @click="expandClick(item.id)"
      />
    </ElTooltip>
  </div>
</template>

<script lang="ts" setup>
import { IconifyIcon } from '@vben/icons';

import { ElTooltip } from 'element-plus';

import { expandBtnList } from '#/utils/common';

const props = defineProps({
  expandIdx: {
    type: Number,
    default: 0,
  },
});

const emit = defineEmits(['expandClick']);

const expandClick = (level: number) => {
  emit('expandClick', level);
};
</script>

<style lang="scss" scoped>
.font-size {
  width: 50px;
  font-size: 14px;
}
.icon-btn {
  width: 20px;
  height: 20px;
  outline: none;
  cursor: pointer;
}
.active {
  color: #006be6;
}
.tree-content {
  height: calc(100vh - 180px);
  overflow: auto;
}
.icon-btn {
  width: 20px;
  height: 20px;
  outline: none;
  cursor: pointer;
  transition: color 0.3s;
}
.icon-btn.active {
  color: #006be6;
}
</style>
