<template>
  <div class="com-context-menu" ref="containerRef">
    <slot></slot>
    <Teleport to="body">
      <div
        v-if="visable"
        :style="{
          position: 'fixed',
          top: `${y}px`,
          left: `${x}px`,
        }"
        class="context-menu flex items-center"
      >
        <div class="menu-list">
          <div
            class="menu-item"
            v-for="item in menu"
            :key="item.label"
            :class="{ 'menu-item-disabled': item.disabled }"
            @click="item.disabled ? null : handleClick(item)"
          >
            {{ item.label }}
          </div>
        </div>
      </div>
    </Teleport>
  </div>
</template>

<script lang="ts" setup>
import type { PropType } from 'vue';

import type { MenuItem } from './type';

import { ref } from 'vue';

import { useContextMenu } from './useContextMenu';
// 接收传递进来的菜单项
defineProps({
  menu: {
    type: Array as PropType<MenuItem[]>,
    default: () => [],
  },
});
const emit = defineEmits(['select']);
const containerRef = ref(null);
// 声明一个事件，选中菜单项的时候返回数据
const { x, y, visable, open, close } = useContextMenu(containerRef);
const handleClick = (item: MenuItem) => {
  emit('select', item);
};
// 暴露方法给父组件
defineExpose({
  open,
  close,
});
</script>

<style lang="scss" scoped>
.context-menu {
  border-radius: 8px;
  width: 130px;
  background-color: #fafafa;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  z-index: 999;
  .menu-list {
    padding: 4px 0 6px 0;
    width: 100%;
    .menu-item {
      // height: 30px;
      // line-height: 13px;
      width: 100%;
      cursor: pointer;
      padding: 8px;
      margin: 4px 0;
      font-size: 14px;
      &:hover {
        background-color: rgb(235.9, 245.3, 255);
      }

      &.menu-item-disabled {
        color: #c0c4cc;
        cursor: not-allowed;
        &:hover {
          background-color: transparent;
        }
      }
    }
    .disabled {
      pointer-events: none;
      opacity: 0.3;
      cursor: not-allowed;
      // color: #ccc;
      &:hover {
        background-color: transparent;
      }
    }
  }
}
</style>
