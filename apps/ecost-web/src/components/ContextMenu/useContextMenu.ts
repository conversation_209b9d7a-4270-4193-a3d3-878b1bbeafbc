import { onMounted, onUnmounted, ref } from 'vue';

export function useContextMenu(containerRef: any) {
  const visable = ref(false);
  const x = ref(0);
  const y = ref(0);
  const handlePos = (e: MouseEvent) => {
    e.preventDefault(); // 阻止浏览器的默认行为
    e.stopPropagation(); // 阻止冒泡
    visable.value = true;
    x.value = e.clientX + 16;
    y.value = e.clientY + 6; // 做一些偏移
  };
  function close() {
    visable.value = false;
  }
  const open = (evt: MouseEvent) => {
    if (evt) {
      handlePos(evt);
    }
    visable.value = true;
  };
  onMounted(() => {
    window.addEventListener('click', close, true);
    window.addEventListener('contextmenu', close, true);
  });
  onUnmounted(() => {
    // 移除事件
    window.removeEventListener('click', close, true);
    window.removeEventListener('contextmenu', close, true);
  });
  return {
    visable,
    x,
    y,
    open,
    close,
  };
}
