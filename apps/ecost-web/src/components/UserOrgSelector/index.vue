<template>
  <div class="org-selector">
    <ElTreeSelect
      class="w-180px"
      v-model="xOrgId"
      :data="data"
      :props="{
        value: 'id',
        label: 'name',
        disabled: (v: TreeData) => !v.isDirect,
        children: 'children',
      }"
      default-expand-all
      filterable
      :check-strictly="true"
      :filter-node-method="filterNode"
      @change="handleChangeOrganization"
    />
  </div>
</template>

<script lang="ts" setup>
import type { TreeData } from './index.interface';

import { defineProps, onBeforeMount, ref } from 'vue';

import { ElTreeSelect } from 'element-plus';

import { getUserOrganizationTree } from '#/api/systemManagementApi/organizationApis';

defineOptions({
  name: 'UserOrgSelector',
});

const props = withDefaults(
  defineProps<{
    orgId: string;
  }>(),
  {
    orgId: '',
  },
);

interface Tree {
  [key: string]: any;
}

const xOrgId = ref(props.orgId);

const data = ref([]);

onBeforeMount(async () => {
  getUserOrganizationTree({}).then((res) => {
    data.value = res;

    if (!xOrgId.value) {
      xOrgId.value = findFirstDirectOrgId(res);
      sessionStorage.setItem('x-org-id', xOrgId.value);
      // window.location.reload();
    }
  });
});

function findFirstDirectOrgId(orgData: any) {
  for (const item of orgData) {
    if (item.isDirect) {
      return item.id;
    }
    if (Array.isArray(item.children)) {
      const result: any = findFirstDirectOrgId(item.children);
      if (result) {
        return result;
      }
    }
  }

  return null;
}

const filterNode = (value: string, data: Tree) => {
  if (!value) return true;
  return data.label.includes(value);
};

// 组织变更
function handleChangeOrganization() {
  sessionStorage.setItem('x-org-id', xOrgId.value);
  window.location.reload();
}
</script>
<style scoped></style>
