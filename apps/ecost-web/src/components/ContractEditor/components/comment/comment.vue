<template>
  <div class="comment" editor-component="comment"></div>
</template>

<script lang="ts" setup>
import { inject } from 'vue';
import Editor from '@hufe921/canvas-editor';
import { commentList } from '../../mock';
import { scrollIntoView } from '../../utils';

interface RangeStyleChangeCallback {
  (payload: any): void;
}
let instance: Editor;
let commentDom: HTMLDivElement;

const registerRangeStyleChange = inject<RangeStyleChangeCallback>(
  'registerRangeStyleChange',
);
function init(editor: Editor) {
  // 模拟批注
  commentDom = document.querySelector<HTMLDivElement>('.comment')!;
  instance = editor;

  // 内部事件监听
  if (registerRangeStyleChange) {
    registerRangeStyleChange((payload: any) => {
      // 批注
      commentDom
        .querySelectorAll<HTMLDivElement>('.comment-item')
        .forEach((commentItemDom) => {
          commentItemDom.classList.remove('active');
        });
      if (payload.groupIds) {
        const [id] = payload.groupIds;
        const activeCommentDom = commentDom.querySelector<HTMLDivElement>(
          `.comment-item[data-id='${id}']`,
        );
        if (activeCommentDom) {
          activeCommentDom.classList.add('active');
          scrollIntoView(commentDom, activeCommentDom);
        }
      }
    });
  }
}

async function updateComment() {
  const groupIds = await instance.command.getGroupIds();
  for (const comment of commentList) {
    const activeCommentDom = commentDom.querySelector(
      `.comment-item[data-id='${comment.id}']`,
    );
    // 编辑器是否存在对应成组id
    if (groupIds.includes(comment.id)) {
      // 当前dom是否存在-不存在则追加
      if (!activeCommentDom) {
        const commentItem = document.createElement('div');
        commentItem.classList.add('comment-item');
        commentItem.setAttribute('data-id', comment.id);
        commentItem.onclick = () => {
          instance.command.executeLocationGroup(comment.id);
        };
        commentDom.append(commentItem);
        // 选区信息
        const commentItemTitle = document.createElement('div');
        commentItemTitle.classList.add('comment-item__title');
        commentItemTitle.append(document.createElement('span'));
        const commentItemTitleContent = document.createElement('span');
        commentItemTitleContent.innerText = comment.rangeText;
        commentItemTitle.append(commentItemTitleContent);
        const closeDom = document.createElement('i');
        closeDom.onclick = () => {
          instance.command.executeDeleteGroup(comment.id);
        };
        commentItemTitle.append(closeDom);
        commentItem.append(commentItemTitle);
        // 基础信息
        const commentItemInfo = document.createElement('div');
        commentItemInfo.classList.add('comment-item__info');
        const commentItemInfoName = document.createElement('span');
        commentItemInfoName.innerText = comment.userName;
        const commentItemInfoDate = document.createElement('span');
        commentItemInfoDate.innerText = comment.createdDate;
        commentItemInfo.append(commentItemInfoName);
        commentItemInfo.append(commentItemInfoDate);
        commentItem.append(commentItemInfo);
        // 详细评论
        const commentItemContent = document.createElement('div');
        commentItemContent.classList.add('comment-item__content');
        commentItemContent.innerText = comment.content;
        commentItem.append(commentItemContent);
        commentDom.append(commentItem);
      }
    } else {
      // 编辑器内不存在对应成组id则dom则移除
      activeCommentDom?.remove();
    }
  }
}

defineExpose({
  init,
  updateComment,
});
</script>

<style>
@import './comment.css';
</style>
