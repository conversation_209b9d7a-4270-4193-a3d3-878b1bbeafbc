<template>
  <div class="catalog" editor-component="catalog">
    <div class="catalog__header">
      <span>大纲</span>
      <div class="catalog__header__close">
        <i></i>
      </div>
    </div>
    <div class="catalog__main"></div>
  </div>
</template>

<script lang="ts" setup>
import type { ICatalogItem } from '@hufe921/canvas-editor';
import { ref } from 'vue';
import Editor from '@hufe921/canvas-editor';

let isCatalogShow = ref(true);
let instance: Editor;
function init(editor: Editor) {
  instance = editor;

  const catalogDom = document.querySelector<HTMLElement>('.catalog')!;
  const catalogModeDom =
    document.querySelector<HTMLDivElement>('.catalog-mode')!;
  const catalogHeaderCloseDom = document.querySelector<HTMLDivElement>(
    '.catalog__header__close',
  )!;
  const switchCatalog = () => {
    isCatalogShow.value = !isCatalogShow.value;
    if (isCatalogShow.value) {
      catalogDom.style.display = 'block';
      updateCatalog();
    } else {
      catalogDom.style.display = 'none';
    }
  };
  catalogModeDom.onclick = switchCatalog;
  catalogHeaderCloseDom.onclick = switchCatalog;
}

async function updateCatalog() {
  const catalog = await instance.command.getCatalog();
  const catalogMainDom =
    document.querySelector<HTMLDivElement>('.catalog__main')!;
  catalogMainDom.innerHTML = '';
  if (catalog) {
    const appendCatalog = (
      parent: HTMLDivElement,
      catalogItems: ICatalogItem[],
    ) => {
      for (let c = 0; c < catalogItems.length; c++) {
        const catalogItem = catalogItems[c];
        const catalogItemDom = document.createElement('div');
        catalogItemDom.classList.add('catalog-item');
        // 渲染
        const catalogItemContentDom = document.createElement('div');
        catalogItemContentDom.classList.add('catalog-item__content');
        const catalogItemContentSpanDom = document.createElement('span');
        catalogItemContentSpanDom.innerText = catalogItem.name;
        catalogItemContentDom.append(catalogItemContentSpanDom);
        // 定位
        catalogItemContentDom.onclick = () => {
          instance.command.executeLocationCatalog(catalogItem.id);
        };
        catalogItemDom.append(catalogItemContentDom);
        if (catalogItem.subCatalog && catalogItem.subCatalog.length) {
          appendCatalog(catalogItemDom, catalogItem.subCatalog);
        }
        // 追加
        parent.append(catalogItemDom);
      }
    };
    appendCatalog(catalogMainDom, catalog);
  }
}

defineExpose({
  init,
  isCatalogShow,
  updateCatalog,
});
</script>

<style>
@import './catalog.css';
</style>
