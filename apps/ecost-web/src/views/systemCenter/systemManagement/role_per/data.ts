/**
 * 分列组件配置
 */
import { ref } from 'vue';

export const colPageProps = ref({
  leftCollapsedWidth: 3,
  leftCollapsible: true,
  leftMaxWidth: 25,
  leftMinWidth: 20,
  leftWidth: 25,
  resizable: true,
  rightWidth: 75,
  splitHandle: true,
  splitLine: true,
  autoContentHeight: true,
});

/**
 * 操作权限视图枚举
 */
export enum ACTIONPERMISSIONVIEWCODE {
  AP_VIEW_EVERYONE = 'AP_VIEW_EVERYONE', // 查看:所有人
  AP_VIEW_NONE = 'AP_VIEW_NONE', // 查看:无查看权限
  AP_VIEW_SELF = 'AP_VIEW_SELF', // 查看:仅自己
}
