<template>
  <div>
    <ElDialog
      v-bind="$attrs"
      v-model="dialogVisible"
      :destroy-on-close="false"
      :width="1000"
      top="5vh"
      @close="handleClose"
    >
      <div class="flex items-center justify-between">
        <ElCard class="w-1/2">
          <template #header>
            <div class="text-sm">选择用户</div>
          </template>
          <!-- 用户列表 -->
          <vxe-grid
            v-bind="userTableOptions"
            @cell-dblclick="handleUserTableClick"
          >
            <template #userOrgSlot="{ row }">
              {{ row.orgs.map((item: any) => item.name).join(',') }}
            </template>
          </vxe-grid>
        </ElCard>
        <IconifyIcon icon="mdi:arrow-right-bottom" class="text-4xl" />
        <ElCard class="w-1/2">
          <template #header>
            <div class="text-sm">确认用户</div>
          </template>
          <vxe-grid v-bind="selectUserTableOptions">
            <template #userOrgSlot="{ row }">
              {{ row.orgs.map((item: any) => item.name).join(',') }}
            </template>
            <template #actionSlot="{ row }">
              <ElButton
                link
                type="danger"
                size="small"
                @click="handleDeleteSelectUser(row)"
              >
                删除
              </ElButton>
            </template>
          </vxe-grid>
        </ElCard>
      </div>
      <template #footer>
        <ElButton size="small" @click="handleClose">取消</ElButton>
        <ElButton size="small" type="primary" @click="handleConfirm">
          确定
        </ElButton>
      </template>
    </ElDialog>
  </div>
</template>
<script lang="ts" setup>
import { reactive, ref, watch } from 'vue';

import { IconifyIcon } from '@vben/icons';

import { ElButton, ElCard, ElDialog, ElMessage } from 'element-plus';

import { filterNotRoleUser } from '#/api/systemManagementApi/role_per';

const props = withDefaults(
  defineProps<{
    nodeData: any;
    visible: boolean;
  }>(),
  {
    visible: false,
    nodeData: () => ({}),
  },
);
const emit = defineEmits<{
  (e: 'update:visible', visible: boolean): void;
  (e: 'refresh'): void;
  (e: 'selectUser', data: any): void;
}>();
watch(
  () => props.visible,
  (newValue) => {
    dialogVisible.value = newValue;
  },
);
const dialogVisible = ref(props.visible);

const userTableOptions = reactive({
  height: 500,
  size: 'mini',
  border: true,
  align: 'center',
  keepSource: true,
  showOverflow: 'title',
  resizable: true,
  headerCellConfig: {
    height: 40,
  },
  rowConfig: {
    isCurrent: true,
    isHover: true,
  },
  columns: [
    {
      type: 'seq',
      width: 50,
    },
    {
      field: 'username',
      title: '姓名',
    },
    {
      field: 'phone',
      title: '电话号码',
    },
    {
      field: 'idCard',
      title: '身份证号',
    },
    {
      field: 'userOrg',
      title: '所属组织',
      slots: {
        default: 'userOrgSlot',
      },
    },
  ],
  data: [],
  pagerConfig: {
    total: 0,
    currentPage: 1,
    pageSize: 10,
    layouts: ['Number', 'Sizes', 'Total'],
    size: 'mini',
  },
  proxyConfig: {
    response: {
      result: 'list', // 指定后端返回中列表字段的名称
      total: 'pageInfo.total', // 指定后端返回中总条数字段的名称
    },
    ajax: {
      query: async ({ page }, formValues) => {
        return await filterNotRoleUser({
          page: page.currentPage,
          pageSize: page.pageSize,
          roleIds: props.nodeData.id,
          ...formValues,
        });
      },
    },
  },
});
// 右侧已选用户列表配置
const selectUserTableOptions = reactive({
  height: 500,
  size: 'mini',
  border: true,
  align: 'center',
  showOverflow: 'title',
  keepSource: true,
  resizable: true,
  rowConfig: {
    isCurrent: true,
    isHover: true,
  },
  columns: [
    {
      type: 'seq',
      width: 50,
    },
    {
      field: 'username',
      title: '姓名',
    },
    {
      field: 'phone',
      title: '电话号码',
    },
    {
      field: 'idCard',
      title: '身份证号',
    },
    {
      field: 'userOrg',
      title: '所属组织',
      slots: {
        default: 'userOrgSlot',
      },
    },
    {
      field: 'action',
      title: '操作',
      slots: {
        default: 'actionSlot',
      },
    },
  ],
  data: [],
});
// 用户选择
function handleUserTableClick({ row }: any) {
  const isUserSelect = selectUserTableOptions.data.some(
    (item: any) => item.id === row.id,
  );
  if (isUserSelect) {
    return ElMessage.warning('用户已被添加，请勿重复添加!');
  }
  selectUserTableOptions.data.push({
    username: row.username,
    phone: row.phone,
    idCard: row.idCard,
    orgs: row.orgs,
    id: row.id,
  });
}
// 删除已选用户
function handleDeleteSelectUser(row: any) {
  selectUserTableOptions.data = selectUserTableOptions.data.filter(
    (item: any) => item.id !== row.id,
  );
}
function handleClose() {
  emit('update:visible', false);
}
function handleConfirm() {
  emit('selectUser', selectUserTableOptions.data);
  emit('update:visible', false);
}
</script>
<style scoped lang="scss">
::v-deep(.el-card__body) {
  padding: 10px;
}
::v-deep(.el-card__header) {
  padding: 5px 10px;
  font-size: 14px;
}
</style>
