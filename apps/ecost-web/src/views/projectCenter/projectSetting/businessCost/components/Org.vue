<template>
  <div>{{ orgName }}</div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';

import { getUserOrganizationTree } from '#/api/systemManagementApi/organizationApis/index.ts';

const props = defineProps({
  orgId: {
    type: Array,
    default: () => [],
  },
});

const orgList = ref([]);
const orgName = computed(() => {
  if (!orgList.value) return ''; // 如果数据未加载完成，返回空字符串
  const names = findNodeNamesById(orgList.value, props.orgId);
  return names.join(', ');
});

async function getOrgTreeList() {
  try {
    orgList.value = await getUserOrganizationTree({});
  } catch (error) {
    console.error('Failed to fetch organization tree:', error);
  }
}

function findNodeNamesById(tree, ids) {
  const result = [];

  function recurseFind(node) {
    if (Array.isArray(node)) {
      for (const item of node) {
        if (ids.includes(item.id)) {
          result.push(item.name);
        }
        if (Array.isArray(item.children) && item.children.length > 0) {
          recurseFind(item.children);
        }
      }
    }
  }

  recurseFind(tree);
  return result;
}

onMounted(() => {
  getOrgTreeList();
});
</script>

<style scoped lang="scss"></style>
