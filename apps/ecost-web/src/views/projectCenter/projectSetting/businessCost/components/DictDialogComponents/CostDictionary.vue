<template>
  <div class="flex">
    <!-- 版本 -->
    <div class="w-64">
      <BasicTitle class="mb-1" title="版本" />
      <vxe-grid
        v-bind="versionOptions"
        ref="versionRef"
        @cell-click="handleVersionCellClick"
      >
        <template #orgSlot="{ row }">
          <OrgName :org-id="[row.orgId]" />
        </template>
        <template #status="{ row }">
          <ElTag size="small" type="success">
            {{ row.status === 'ENABLED' ? '已启用' : '' }}
          </ElTag>
        </template>
      </vxe-grid>
    </div>

    <!-- 分类 -->
    <div class="ml-2 mr-2 w-96">
      <BasicTitle class="mb-1" title="分类" />
      <vxe-grid
        v-bind="categoryOptions"
        ref="categoryRef"
        @cell-click="handleCategoryCellClick"
        @checkbox-change="handleCategoryCheckBoxChange"
        @checkbox-all="handleCategoryCheckBoxChange"
      >
        <template #typeSlot="{ row }">
          {{ materialDict.find((item) => item.value === row.type)?.label }}
        </template>
      </vxe-grid>
    </div>

    <!-- 明细 -->
    <div class="w-32 flex-1">
      <BasicTitle class="mb-1" title="明细" />
      <vxe-grid
        v-bind="detailOptions"
        ref="detailRef"
        @cell-click="handleDetailCellClick"
        @checkbox-change="handleDetailCheckBoxChange"
        @checkbox-all="handleDetailCheckBoxChange"
      >
        <template #typeSlot="{ row }">
          {{ materialDict.find((item) => item.value === row.type)?.label }}
        </template>
        <template #businessCostSubjectSlot="{ row }">
          {{ filterBusinessCostAccountLabel(row.businessCostSubjectDetailIds) }}
        </template>
      </vxe-grid>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineEmits, defineProps, nextTick, reactive, ref, watch } from 'vue';

import { ElMessage, ElTag } from 'element-plus';
import _ from 'lodash';

import { TreeBusinessCostCategoryDetails } from '#/api/enterpriseCenter/enterpriseStandards/businessCostSubject';
import {
  CategoryList,
  DetailsList,
  VersionList,
} from '#/api/projectCenter/projectSetting/businessCost';
import BasicTitle from '#/components/BasicTitleBar/index.vue';
import { getNamesFromTreeByIds } from '#/utils/common';

import { materialDict } from '../../data';
import OrgName from '../Org.vue';

const props = withDefaults(
  defineProps<{
    dictType: string;
    dictTypeId: string;
  }>(),
  {
    dictTypeId: '',
    dictType: 'MATERIAL_DICTIONARY',
  },
);
const emit = defineEmits<{
  (e: 'componentsEvent', payload: Object): void;
}>();

const selectCategoryIds = ref([]); // 已选择的分类
const selectDetails = ref([]); // 已选择版本

const currentVersion = ref({});
const versionRef = ref();
const versionOptions = reactive({
  height: 700,
  size: 'mini',
  border: true,
  align: 'center',
  keepSource: true,
  rowConfig: {
    isCurrent: true,
    isHover: true,
  },
  columnConfig: {
    resizable: true,
  },
  checkboxConfig: {
    showHeader: false,
  },
  columns: [
    {
      field: 'name',
      title: '名称',
      showOverflow: 'title',
    },
    {
      field: 'name',
      title: '编制单位',
      slots: {
        default: 'orgSlot',
      },
    },
    {
      field: 'status',
      title: '状态',
      slots: {
        default: 'status',
      },
    },
  ],
  data: [],
});
async function handleVersionCellClick({ row }) {
  currentVersion.value = row;
  categoryOptions.data = await CategoryList(props.dictTypeId, row.id);
  // 过滤出已选中的数据
  const isUseIds = categoryOptions.data
    .filter((item) => item.isUse)
    .map((item) => item.id);
  if (isUseIds.length > 0) {
    await nextTick(() => {
      categoryRef.value.setCheckboxRowKey(isUseIds, true);
    });
  }
  await nextTick(() => {
    categoryRef.value.setAllTreeExpand(true);
  });
  // 切换版本数据置空
  selectCategoryIds.value = [];
  selectDetails.value = [];
}
// 分类
const categoryRef = ref();
const categoryOptions = reactive({
  height: 700,
  size: 'mini',
  border: true,
  align: 'center',
  keepSource: true,
  rowConfig: {
    isCurrent: true,
    isHover: true,
    keyField: 'id',
  },
  treeConfig: {
    rowField: 'id',
    parentField: 'parentId',
    transform: true,
    expandAll: true,
  },
  columnConfig: {
    resizable: true,
  },
  checkboxConfig: {
    checkMethod: ({ row }: { row: any }) => {
      return !row.isUse;
    },
  },
  columns: [
    {
      type: 'checkbox',
      treeNode: true,
      width: '120px',
    },
    {
      field: 'code',
      title: '编码',
    },
    {
      field: 'name',
      title: '名称',
    },
    {
      field: 'remark',
      title: '备注',
    },
  ],
  data: [],
});
async function handleCategoryCellClick({ row }) {
  const dictTypeId = props.dictTypeId;
  const versionId = row.versionId;
  const categoryId = row.id;
  detailOptions.data = await DetailsList(dictTypeId, versionId, categoryId);

  // 过滤出已选中的数据
  const isUseIds = detailOptions.data
    .filter((item) => item.isUse)
    .map((item) => item.id);
  if (isUseIds.length > 0) {
    await nextTick(() => {
      detailRef.value.setCheckboxRowKey(isUseIds, true);
    });
  }

  // 切换分类数据置空
  selectDetails.value = [];
}
// 分类选择
function handleCategoryCheckBoxChange({
  records,
  indeterminates,
}: {
  indeterminates: any;
  records: any;
}) {
  // 全选与半选数据拼接过去掉已使用的数据
  const selectAllList = [...records, ...indeterminates];
  selectCategoryIds.value = selectAllList
    .filter((item) => !item.isUse)
    .map((item) => item.id);
  emit('componentsEvent', {
    versionId: currentVersion.value.id,
    categoryIds: selectCategoryIds.value,
    details: selectDetails.value,
  });
}
// 明细选择
function handleDetailCheckBoxChange({ records }: { records: any }) {
  selectDetails.value = records.map((item: any) => {
    return {
      categoryId: item.categoryId,
      detailId: item.id,
    };
  });
  emit('componentsEvent', {
    versionId: currentVersion.value.id,
    categoryIds: selectCategoryIds.value,
    details: selectDetails.value,
  });
}
// 明细
const detailRef = ref();
const detailOptions = reactive({
  height: 700,
  size: 'mini',
  border: true,
  align: 'center',
  keepSource: true,
  rowConfig: {
    isCurrent: true,
    isHover: true,
    keyField: 'id',
  },
  columnConfig: {
    resizable: true,
  },
  checkboxConfig: {
    checkMethod: ({ row }: { row: any }) => {
      return !row.isUse;
    },
  },
  columns: [
    {
      type: 'checkbox',
      width: '60px',
    },
    {
      width: 100,
      title: '编码',
      field: 'code',
    },
    {
      width: 120,
      field: 'name',
      title: '名称',
    },
    {
      field: 'specificationModel',
      title: '规格型号',
    },
    {
      width: 120,
      field: 'remark',
      title: '备注',
    },
    {
      field: 'businessCostSubjectDetailsIds',
      title: '业务成本科目名称',
      slots: {
        default: 'businessCostSubjectSlot',
      },
    },
    {
      width: 120,
      field: 'accountingDescription',
      title: '核算说明',
    },
  ],
  data: [],
});
function handleDetailCellClick() {}

// 分类校验
function categoryValid(): boolean {
  const allRows = categoryOptions.data;
  const usableRows = allRows.filter((item) => !item.isUse);

  if (_.isEmpty(currentVersion.value)) {
    ElMessage.warning('请选择版本！');
    return false;
  }

  if (usableRows.length === 0) {
    ElMessage.warning('所有分类数据已被使用！');
    return false;
  }

  if (selectCategoryIds.value.length === 0) {
    ElMessage.warning('请选择分类');
    return false;
  }

  return true;
}

// 明细校验
function detailValid(): boolean {
  const allRows = detailOptions.data;
  const usableRows = allRows.filter((item) => !item.isUse);

  if (_.isEmpty(currentVersion.value)) {
    ElMessage.warning('请选择版本！');
    return false;
  }

  if (selectDetails.value.length === 0) {
    ElMessage.warning('请选择明细');
    return false;
  }

  if (usableRows.length === 0) {
    ElMessage.warning('所有明细数据已被使用！');
    return false;
  }

  return true;
}

function validateAll(): boolean {
  return categoryValid() && detailValid();
}

const businessCostAccountTreeList = ref([]);
// 获取业务成本科目树
async function getBusinessCostAccount(businessVersionId: string) {
  businessCostAccountTreeList.value =
    await TreeBusinessCostCategoryDetails(businessVersionId);
}
// 业务成本科目树选中
function filterBusinessCostAccountLabel(ids: [string]) {
  return getNamesFromTreeByIds(businessCostAccountTreeList.value, ids);
}
watch(
  () => props.dictType,
  () => {
    getVersionList(props.dictTypeId);
    getBusinessCostAccount(currentVersion.value.businessCostSubjectVersionId);
  },
  {
    immediate: true,
  },
);

// 获取分类
async function getVersionList(dictTypeId: string) {
  versionOptions.data = await VersionList(dictTypeId);
}

defineExpose({
  validateAll,
});
</script>

<style scoped lang="scss"></style>
