<template>
  <vxe-grid
    class="bg-card rounded-lg border p-2"
    style="width: 300px"
    v-bind="categoryListOptions"
    ref="categoryListRef"
    @cell-click="handCategoryListCellClick"
    @menu-click="handleCategoryRightContextMenuClick"
  >
    <template #typeSlot="{ row }">
      {{ materialDict.find((item) => item.value === row.type)?.label }}
    </template>
  </vxe-grid>
</template>

<script setup lang="ts">
import {
  defineEmits,
  defineProps,
  nextTick,
  onMounted,
  reactive,
  ref,
  watch,
} from 'vue';

import { ElMessage, ElMessageBox } from 'element-plus';

import {
  DeleteSelectCategoryData,
  DeleteSelectData,
  SelectVersionList,
} from '#/api/projectCenter/projectSetting/businessCost';

import { materialDict } from '../../../data';

const props = withDefaults(
  defineProps<{
    dictTypeId: string;
  }>(),
  {
    dictTypeId: '',
  },
);
const emit = defineEmits<{
  (e: 'categoryEvent', payload: Object): void;
  (e: 'categoryRefresh'): void;
}>();

const categoryListOptions = reactive({
  size: 'mini',
  border: true,
  keepSource: true,
  align: 'center',
  showOverflow: true,
  columnConfig: {
    resizable: true,
  },
  treeConfig: {
    rowField: 'id',
    parentField: 'parentId',
    transform: true,
    expandAll: true,
  },
  rowConfig: {
    isCurrent: true,
  },
  columns: [
    {
      field: 'code',
      title: '编码',
    },
    {
      field: 'name',
      title: '名称',
    },
    {
      field: 'remark',
      title: '备注',
    },
  ],
  data: [],
  menuConfig: {
    body: {
      options: [
        [
          {
            code: 'DELETE_ROW',
            name: '删除行',
            disabled: false,
            prefixConfig: { icon: 'vxe-icon-delete-fill' },
          },
        ],
      ],
    },
    // visibleMethod: ({ options, column, row }) => {
    //   if (Object.hasOwn(row, 'dataId')) {
    //     options[0].forEach((item: any) => {
    //       item.disabled = false;
    //     });
    //     return true;
    //   }
    // },
  },
});
const categoryListRef = ref();
const categoryCurrent = ref({});
async function handCategoryListCellClick({ row }: { row: any }) {
  categoryCurrent.value = row;
  emit('categoryEvent', {
    categoryId: row.id,
    versionId: row.versionId,
    quoteId: row.businessCostSubjectVersionId, // 引用id
  });
}
async function handleCategoryRightContextMenuClick({ menu }) {
  if (menu.code === 'DELETE_ROW') {
    ElMessageBox.confirm('确定删除该数据？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }).then(async () => {
      // 删除版本
      if (Object.hasOwn(categoryCurrent.value, 'dataId')) {
        const res = await DeleteSelectData(
          categoryCurrent.value.id,
          props.dictTypeId,
        );
        if (res) ElMessage.success('操作成功！');
      } else {
        // 删除分类
        const res = await DeleteSelectCategoryData(
          categoryCurrent.value.versionId,
          categoryCurrent.value.id,
          props.dictTypeId,
        );
        if (res) ElMessage.success('操作成功！');
      }
      emit('categoryRefresh');
    });
  }
}

// 获取分类数据
async function loadCategoryData() {
  if (!props.dictTypeId) return;

  categoryListOptions.data = await SelectVersionList(props.dictTypeId);
  await nextTick();
  categoryListRef.value?.setAllTreeExpand(true);
}

// 添加手动刷新方法
const refreshData = () => loadCategoryData();

// 初始加载 + 监听ID变化
onMounted(refreshData);
watch(() => props.dictTypeId, refreshData);

// 暴露方法给父组件
defineExpose({ refreshData });
</script>

<style scoped lang="scss"></style>
