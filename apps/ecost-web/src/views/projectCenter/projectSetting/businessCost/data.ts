import { ref } from 'vue';

/**
 * 分列组件配置
 */
export const colPageProps = ref({
  leftCollapsedWidth: 3,
  leftCollapsible: true,
  leftMaxWidth: 20,
  leftMinWidth: 15,
  leftWidth: 15,
  resizable: true,
  rightWidth: 85,
  splitHandle: true,
  splitLine: true,
  autoContentHeight: true,
});

// / 字典类型
export enum DICT_TYPE {
  BUSINESS_COST_SUBJECT = 'BUSINESS_COST_SUBJECT', // 业务成本科目发布
  COST_DICTIONARY = 'COST_DICTIONARY', // 费用字典发布
  MATERIAL_DICTIONARY = 'MATERIAL_DICTIONARY', // 材料字典发布
  MECHANICAL_DICTIONARY = 'MECHANICAL_DICTIONARY', // 机械字典发布
  SUBCONTRACT_STANDARD_BILL = 'SUBCONTRACT_STANDARD_BILL', // 分包标准清单
  TAXRATE_DICTIONARY = 'TAXRATE_DICTIONARY', // 税率字典发布
}

// 材料字典
export const materialDict = [
  {
    label: '商品混凝土',
    value: 'CONCRETE',
  },
  {
    label: '消耗材料',
    value: 'CONSUME_MATERIAL',
  },
  {
    label: '固定资产/低值易耗品',
    value: 'FIXEDASSETSL_CONSUMABLES',
  },
  {
    label: '周转材料',
    value: 'TURNOVERME_MATERIAL',
  },
];
export const dictTypeList = [
  {
    label: '业务成本科目',
    value: 'BUSINESS_COST_SUBJECT',
  },
  {
    label: '材料字典',
    value: 'MATERIAL_DICTIONARY',
  },
  {
    label: '费用字典',
    value: 'COST_DICTIONARY',
  },
  {
    label: '机械字典',
    value: 'MECHANICAL_DICTIONARY',
  },
  {
    label: '分包标准清单',
    value: 'SUBCONTRACT_STANDARD_BILL',
  },
  {
    label: '税率字典',
    value: 'TAXRATE_DICTIONARY',
  },
];

// 业务成本科目明细
export const businessCostColumnsData = ref([
  {
    title: '编码',
    field: 'code',
  },
  {
    width: 150,
    field: 'name',
    title: '业务成本科目名称',
  },
  {
    field: 'unit',
    title: '单位',
  },
  {
    field: 'expenseCategory',
    title: '费用类别',
  },
  {
    field: 'accountingDescription',
    title: '核算说明',
  },
  {
    field: 'isSafetyConstructionFee',
    title: '安全施工费',
    slots: {
      default: 'isSafetyConstructionFee-slot',
    },
  },
  {
    field: 'financialCostSubjectId',
    title: '财务成本科目对照',
    width: 120,
    slots: {
      default: 'financialCostAccountSlot_default',
    },
  },
  {
    field: 'subjectMappingDescription',
    title: '科目对照说明',
  },
]);
// 材料字典发布明细
export const materialDictionaryColumnsData = ref([
  {
    title: '编码',
    field: 'code',
  },
  {
    width: 150,
    field: 'name',
    title: '名称',
  },
  {
    width: 150,
    field: 'specificationModel',
    title: '规格型号',
  },
  {
    field: 'meteringUnit',
    title: '计量单位',
  },
  {
    field: 'type',
    title: '核算类型',
  },
  {
    field: 'remark',
    title: '备注',
  },
  {
    field: 'businessCostSubjectDetailsIds',
    title: '业务成本科目名称',
  },
  {
    field: 'accountingDescription',
    title: '核算说明',
  },
]);
