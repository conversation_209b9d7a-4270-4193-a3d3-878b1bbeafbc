<template>
  <Page auto-content-height>
    <div class="bg-card h-full rounded-lg border p-2">
      <div class="flex justify-end" v-show="formItemList.length > 0">
        <ElButton type="primary" size="small" @click="submit"> 保存 </ElButton>
      </div>
      <ElScrollbar>
        <ElEmpty
          v-if="formItemList.length === 0"
          description="暂无发布字段信息！"
        />
        <ElForm
          v-else
          size="small"
          ref="formRef"
          :model="formData"
          label-width="auto"
          label-position="top"
        >
          <div v-for="item in formItemList" :key="item.id">
            <BasicTitleBar :title="item.name" class="mb-2" />
            <ElRow :gutter="20">
              <ElCol
                :span="8"
                v-for="field in item.basicProjectInfoFieldDetail"
                :key="field.id"
              >
                <ElFormItem
                  :label="field.name"
                  :prop="getFieldKey(field.id)"
                  :rules="
                    field.isRequired
                      ? [
                          {
                            required: true,
                            message: `请输入${field.name}`,
                            trigger: ['blur', 'change'],
                          },
                        ]
                      : []
                  "
                >
                  <!--   文本框    -->
                  <ElInput
                    v-if="field.type === 'TEXT'"
                    :placeholder="field.placeholder"
                    v-model="formData[getFieldKey(field.id)]"
                  >
                    <template v-if="field.unit" #append>
                      {{ field.unit }}
                    </template>
                  </ElInput>
                  <!--   数值    -->
                  <ElInputNumber
                    style="width: 100%"
                    class="number-input"
                    v-if="field.type === 'NUMBER'"
                    controls-position="right"
                    v-model="formData[getFieldKey(field.id)]"
                    :placeholder="field.placeholder"
                  >
                    <template #suffix v-if="field.unit">
                      <span>{{ field.unit }}</span>
                    </template>
                  </ElInputNumber>
                  <!--   日期   -->
                  <ElDatePicker
                    v-if="field.type === 'DATETIME'"
                    v-model="formData[getFieldKey(field.id)]"
                    type="date"
                    value-format="YYYY-MM-DD"
                    :placeholder="field.placeholder"
                  />
                  <!--   枚举下拉  -->
                  <ElSelect
                    v-if="field.type === 'ENUM_SINGLE_CHOICE'"
                    v-model="formData[getFieldKey(field.id)]"
                    :placeholder="field.placeholder"
                  >
                    <ElOption
                      v-for="valueList in field.enumValueList"
                      :key="valueList.value"
                      :label="valueList.label"
                      :value="valueList.value"
                    />
                  </ElSelect>
                  <!--   下拉多选 -->
                  <ElSelect
                    v-if="field.type === 'ENUM_MULTIPLE_CHOICE'"
                    v-model="formData[getFieldKey(field.id)]"
                    multiple
                    :placeholder="field.placeholder"
                  >
                    <ElOption
                      v-for="valueList in field.enumValueList"
                      :key="valueList.value"
                      :label="valueList.label"
                      :value="valueList.value"
                    />
                  </ElSelect>
                </ElFormItem>
              </ElCol>
            </ElRow>
          </div>
        </ElForm>
      </ElScrollbar>
    </div>
  </Page>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue';

import { Page } from '@vben/common-ui';

import {
  ElButton,
  ElCol,
  ElDatePicker,
  ElEmpty,
  ElForm,
  ElFormItem,
  ElInput,
  ElInputNumber,
  ElMessage,
  ElOption,
  ElRow,
  ElScrollbar,
  ElSelect,
} from 'element-plus';

import {
  AllFormItemList,
  SaveItFormItemList,
} from '#/api/projectCenter/projectSetting/projectInfo.ts';
import BasicTitleBar from '#/components/BasicTitleBar/index.vue';

const formRef = ref();
const formData = reactive({});

function submit() {
  formRef.value.validate(async (valid) => {
    if (valid) {
      const payload = formItemList.value.flatMap((group) =>
        group.basicProjectInfoFieldDetail.map((field) => {
          const rawValue = formData[getFieldKey(field.id)];
          let value = rawValue;

          if (
            field.type === 'ENUM_MULTIPLE_CHOICE' &&
            Array.isArray(rawValue)
          ) {
            value = rawValue.join(',');
          } else if (
            field.type === 'NUMBER' &&
            rawValue !== null &&
            rawValue !== undefined
          ) {
            value = String(rawValue);
          }

          return {
            id: field.id,
            value: value ?? '',
          };
        }),
      );
      console.log(payload, '@@@@');
      const res = await SaveItFormItemList({
        data: payload,
      });
      if (res) ElMessage.success('操作成功！');
      getFormItemList();
    }
  });
}
const formItemList = ref([]);
function getFieldKey(fieldId: string) {
  return `${fieldId}`;
}
// 获取所有配置的表单项
async function getFormItemList() {
  const list = await AllFormItemList();
  formItemList.value = list;

  list.forEach((group) => {
    group.basicProjectInfoFieldDetail.forEach((field) => {
      const key = getFieldKey(field.id);
      if (field.type === 'NUMBER') {
        formData[key] =
          field.value !== undefined && field.value !== null
            ? Number(field.value)
            : null;
      } else if (field.type === 'ENUM_MULTIPLE_CHOICE') {
        formData[key] =
          typeof field.value === 'string' && field.value
            ? field.value.split(',')
            : [];
      } else {
        formData[key] = field.value ?? '';
      }
    });
  });
}

getFormItemList();
</script>

<style scoped lang="scss">
:deep(.number-input) {
  .el-input__inner {
    text-align: left;
  }
}
</style>
