import { ref } from 'vue';

/**
 * 分列组件配置
 */
export const colPageProps = ref({
  leftCollapsedWidth: 3,
  leftCollapsible: true,
  leftMaxWidth: 25,
  leftMinWidth: 20,
  leftWidth: 20,
  resizable: true,
  rightWidth: 80,
  splitHandle: true,
  splitLine: true,
  autoContentHeight: true,
});

/**
 * 启用状态枚举
 */
export enum EnableStatus {
  ENABLED = 'ENABLED', //  已启用
  NOT_ENABLED = 'NOT_ENABLED', //  未启用
}

/**
 * 字段类型枚举
 */

export const FieldType = [
  { label: '文本', value: 'TEXT' },
  { label: '数字', value: 'NUMBER' },
  { label: '日期', value: 'DATETIME' },
  { label: '下拉-多选', value: 'ENUM_MULTIPLE_CHOICE' },
  { label: '下拉-单选', value: 'ENUM_SINGLE_CHOICE' },
];
