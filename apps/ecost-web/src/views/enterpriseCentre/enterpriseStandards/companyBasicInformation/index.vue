<template>
  <ColPage v-bind="colPageProps">
    <template #left="{ isCollapsed, expand }">
      <div v-if="isCollapsed" @click="expand">
        <ElTooltip content="点击展开左侧">
          <ElButton circle round type="primary">
            <template #icon>
              <IconifyIcon class="text-2xl" icon="bi:arrow-right" />
            </template>
          </ElButton>
        </ElTooltip>
      </div>
      <!-- 左侧表格区域 -->
      <div :style="{ minWidth: '200px', overflow: 'hidden' }" v-else class="bg-card h-full rounded-lg border p-2 pb-20">
        <div class="mb-2 flex items-center">
          <ElButton type="primary" size="default" @click="handleAdd">新增</ElButton>
        </div>
        <vxe-grid ref="leftGridRef" v-bind="leftGridOptions" v-on="leftMethodOptions">
          <template #status="{ row }">
            <!-- <div>{{ row.status }}</div> -->
            <ElTag :type="row.status === versionStatus.PUBLISHED
              ? 'success'
              : 'primary'
              ">
              {{ getStatusLabel(row.status) }}
            </ElTag>
          </template>
        </vxe-grid>
      </div>
    </template>
    <div class="bg-card ml-2 h-full rounded-lg border p-2 pb-5">
      <div class="mb-2 flex items-center">
        <ElButton type="primary" class="w-[100px]" size="default" :disabled="disabledSave" @click="handleSave">保存
        </ElButton>
        <ElButton type="primary" class="w-[100px]" size="default" :disabled="disabledPublish" @click="handlePublish">
          {{ orgInfoForm.status == versionStatus.PUBLISHED ? '取消发布' : '发布' }}
        </ElButton>
      </div>
      <div v-if="orgInfoForm.id">
        <div class="p-10">
          <el-form ref="formEl" :model="orgInfoForm" :rules="rules" lass="demo-form-inline">
            <el-descriptions :column="3" border size="default">
              <el-descriptions-item label="公司名称">
                <template #label>
                  <div class="cell-item flex ">
                    <div class="text-red-600 mt-[-4px]">*</div>
                    <div>公司名称</div>
                  </div>
                </template>

                <el-form-item prop="companyName" label="">
                  <el-input class="h-[38px]" :disabled="orgInfoForm.status == versionStatus.PUBLISHED"
                    v-model="orgInfoForm.companyName" placeholder="" />
                </el-form-item>
              </el-descriptions-item>
              <el-descriptions-item label="统一社会信用代码">
                <template #label>
                  <div class="cell-item flex ">
                    <div class="text-red-600 mt-[-4px]">*</div>
                    <div>统一社会信用代码</div>
                  </div>
                </template>
                <el-form-item prop="unifiedSocialCreditCode" label="">
                  <el-input class="h-[38px]" :disabled="orgInfoForm.status == versionStatus.PUBLISHED"
                    v-model="orgInfoForm.unifiedSocialCreditCode" placeholder="" />
                </el-form-item>
              </el-descriptions-item>
              <el-descriptions-item label="注册地址">
                <el-form-item prop="registeredAddress" label="">
                  <el-input class="h-[38px]" :disabled="orgInfoForm.status == versionStatus.PUBLISHED"
                    v-model="orgInfoForm.registeredAddress" placeholder="" />
                </el-form-item>
              </el-descriptions-item>
              <el-descriptions-item label="企业所在地">
                <el-form-item prop="companyLocation" label="">
                  <el-input class="h-[38px]" :disabled="orgInfoForm.status == versionStatus.PUBLISHED"
                    v-model="orgInfoForm.companyLocation" placeholder="" />
                </el-form-item>
              </el-descriptions-item>
              <el-descriptions-item label="纳税人身份">
                <el-form-item prop="taxpayerType" label="">
                  <el-select :disabled="orgInfoForm.status == versionStatus.PUBLISHED"
                    v-model="orgInfoForm.taxpayerType" size="large">
                    <el-option :label="'一般纳税人'" :value="'一般纳税人'" />
                    <el-option :label="'小规模纳税人'" :value="'小规模纳税人'" />
                  </el-select>
                </el-form-item>
              </el-descriptions-item>
              <el-descriptions-item label="业务电话">
                <el-form-item prop="businessPhone" label="">
                  <el-input class="h-[38px]" :disabled="orgInfoForm.status == versionStatus.PUBLISHED"
                    v-model="orgInfoForm.businessPhone" placeholder="" />
                </el-form-item>
              </el-descriptions-item>
              <el-descriptions-item label="开户银行">
                <el-form-item prop="bankName" label="">
                  <el-input class="h-[38px]" :disabled="orgInfoForm.status == versionStatus.PUBLISHED"
                    v-model="orgInfoForm.bankName" placeholder="" />
                </el-form-item>
              </el-descriptions-item>
              <el-descriptions-item label="开户地址">
                <el-form-item prop="bankAddress" label="">
                  <el-input class="h-[38px]" :disabled="orgInfoForm.status == versionStatus.PUBLISHED"
                    v-model="orgInfoForm.bankAddress" placeholder="" />
                </el-form-item>
              </el-descriptions-item>
              <el-descriptions-item label="开户账号">
                <el-form-item prop="bankAccount" label="">
                  <el-input class="h-[38px]" :disabled="orgInfoForm.status == versionStatus.PUBLISHED"
                    v-model="orgInfoForm.bankAccount" placeholder="" />
                </el-form-item>
              </el-descriptions-item>
            </el-descriptions>
          </el-form>
        </div>
        <div class="absolute top-0  bg-[#eee] p-4 w-[400px] h-full text-[14px] rounded-[12px]"
          :style="{ right: shouldLogShow ? '0' : '-400px', transition: 'right 0.3s cubic-bezier(0.4,0,0.2,1)' }">
          <div class="top h-[40px] flex justify-between">
            <div class="title text-[18px]">变更记录</div>
            <div @click="changeLogStatus('close')" class="btn-close text-[18px] cursor-pointer ">
              <el-icon>
                <Close class="text-black" />
              </el-icon>
            </div>
          </div>
          <div class="content h-[calc(100%_-_60px)] overflow-scroll">
            <div class="bg-white mb-4 p-4 rounded-[6px]" v-if="changeLogData && changeLogData?.length > 0"
              v-for="(item) in changeLogData" :key="item.id">
              <div class="flex justify-between">
                <div>{{ item.name }}</div>
                <div>{{ dayjs(item.time).format('YYYY年MM月DD日 HH点mm分') }}</div>
                <div class="text-orange-400">有{{ item.data?.length }}处修改</div>
              </div>
              <div class="mt-4" v-for="(v, i) in item.data" :key="i">
                <div class="mb-2">{{ filedKeyName[v.fieldKey] }}:</div>
                <div
                  class="text-[12px] bg-red-200 leading-[28px] min-h-[28px] rounded-[4px] mb-[4px] pl-2 line-through">
                  {{
                    v.oldValue
                  }}
                </div>
                <div class="text-[12px] bg-green-200 leading-[28px] min-h-[28px] rounded-[4px] pl-2">{{ v.newValue }}
                </div>
              </div>
            </div>
            <div class="bg-white mb-4 p-4 rounded-[6px]" v-else>暂无变更记录</div>
          </div>

          <div @click="changeLogStatus()"
            class="cursor-pointer absolute top-[50%] translate-y-[-50%] left-[-30px] w-[30px] bg-green-500 text-white text-center p-[5px]">
            变更记录</div>
        </div>
      </div>
      <div v-else>
        <div class="p-10 flex justify-center items-center">请先新增版本</div>
      </div>
    </div>
  </ColPage>
</template>

<script setup lang="ts">
import { addBusinessVersion, cancelPublishBusinessInfo, delBusinessInfo, getBusinessInfoChangeLogList, getBusinessInfoList, moveBusinessInfo, publishBusinessInfo, saveBusinessInfo, updateBusinessVersion } from '#/api/enterpriseCenter/companyBasicInfo/companyBasicInfo';
import { Close } from '@element-plus/icons-vue';
import { ColPage } from '@vben/common-ui';
import dayjs from 'dayjs';
import { ElButton, ElDescriptions, ElDescriptionsItem, ElForm, ElFormItem, ElIcon, ElInput, ElMessage, ElMessageBox, ElOption, ElSelect, ElTag, ElTooltip } from 'element-plus';
import { computed, nextTick, onMounted, reactive, ref } from 'vue';
import { colPageProps } from './data';
enum versionStatus {
  DRAFT = 'DRAFT', // 草稿状态
  SUBMITTED = 'SUBMITTED', //  已保存，等待发布
  PUBLISHED = 'PUBLISHED' // 已发布（线上版本）
}
const filedKeyName = {
  companyName: "公司名称",
  unifiedSocialCreditCode: "统一社会信用代码",
  registeredAddress: "注册地址",
  companyLocation: "企业所在地",
  taxpayerType: "纳税人身份",
  businessPhone: "业务电话",
  bankName: "开户银行",
  bankAddress: "开户地址",
  bankAccount: "开户账号",
} as any
const getStatusLabel = (status: versionStatus) => {
  const statusMap = {
    [versionStatus.DRAFT]: '未发布',
    [versionStatus.SUBMITTED]: '未发布',
    [versionStatus.PUBLISHED]: '已发布',
  };
  return statusMap[status] || '未知状态';
}
const rules = reactive({
  companyName: [
    { required: true, message: '请输入公司名称', trigger: 'blur' },
  ],
  unifiedSocialCreditCode: [
    { required: true, message: '请输入统一社会信用代码', trigger: 'blur' },
    { pattern: /^[A-Za-z0-9]{18}$/, message: '请输入由字母和数字组成的18位字符', trigger: 'blur' },
  ]
})

let isFinishInsert = true;
const isChoiceOrg = ref(false);
const leftGridRef = ref();
const shouldLogShow = ref(false);
const formEl = ref()
// 公司基本信息表单
const orgInfoForm = reactive({
  id: '',
  companyName: "",
  unifiedSocialCreditCode: "",
  registeredAddress: "",
  companyLocation: "",
  taxpayerType: "",
  businessPhone: "",
  bankName: "",
  bankAddress: "",
  bankAccount: "",
  status: null,
})
const disabledSave = computed(() => {
  return orgInfoForm.id == '' || !isFinishInsert || (!orgInfoForm.status || orgInfoForm.status == versionStatus.PUBLISHED)
})
const disabledPublish = computed(() => {
  return orgInfoForm.id == '' || !isFinishInsert || (!orgInfoForm.status || orgInfoForm.status == versionStatus.DRAFT)
})

// 表格数据配置
const leftGridOptions = reactive<any>({
  height: '98%',
  size: 'mini',
  border: true,
  keepSource: true,
  align: 'center',
  showOverflow: true,
  loading: true,
  // headerRowClassName: 'bg-[skyblue]',
  // rowClassName: ({ row }: { row: { parentId: string | null } }) => {
  //   if (row.parentId) {
  //     return '';
  //   } else {
  //     return 'bg-gray-100';
  //   }
  // },
  columnConfig: {
    resizable: true,
  },
  rowConfig: {
    isCurrent: true,
  },
  // treeConfig: {
  //   rowField: 'id',
  //   parentField: 'parentId',
  //   transform: true,
  //   expandAll: true,
  // },
  editConfig: {
    trigger: 'dblclick',
    mode: 'cell',
    showStatus: true,
    beforeEditMethod({ row }: any) {
      return row.status !== versionStatus.PUBLISHED;
    }
  },
  editRules: {
    name: [{ required: true, message: '名称不得为空！' }],
  },
  columns: [
    {
      type: 'seq',
      field: 'seq',
      title: ' ',
      width: 80,
    },
    {
      field: 'companyVersion',
      title: '名称',
      editRender: {
        name: 'VxeInput',
        props: {
          placeholder: '请输入名称',
        },
      },
    },
    {
      field: 'status',
      title: '状态',
      slots: {
        default: 'status',
      },
    },
    {
      field: 'remark',
      title: '备注',
      editRender: {
        name: 'VxeInput',
        props: {
          placeholder: '请输入名称',
        },
      },
    },
  ],
  menuConfig: {
    body: {
      options: [
        [
          {
            code: 'del',
            name: '删除',
            disabled: false,
            prefixConfig: { icon: 'vxe-icon-delete-fill' },
          },
          {
            code: 'up',
            name: '上移',
            disabled: false,
            prefixConfig: { icon: 'vxe-icon-arrows-up' },
          },
          {
            code: 'down',
            name: '下移',
            disabled: false,
            prefixConfig: { icon: 'vxe-icon-arrows-down' },
          }
        ],
      ],
    },
    visibleMethod: ({ options, column, row, rowIndex }: any) => {
      const isDisabled = row.status === versionStatus.PUBLISHED;

      options[0].forEach((item: any) => {
        switch (item.code) {
          case 'del': {
            item.disabled = isDisabled;
            break;
          }
          case 'up': {
            item.disabled = (rowIndex == 0);
            break;
          }
          case 'down': {
            item.disabled = (rowIndex == leftGridOptions.data.length - 1);
            break;
          }
          // 其他按钮始终可用
          default: {
            item.disabled = false;
          }
        }
      });
      return true;
    },
  },
  data: [],
});
const leftMethodOptions = reactive({
  filterChange(data: any) {

  },
  // 表格确认修改
  async editClosed({ row }: any) {
    insureAddorEdit(row)
  },
  // 点击列
  cellClick({ row }: { row: any }) {

    handleChoice(row)

  },
  // 右键菜单
  cellMenu({ row }: { row: any }) {
    const $grid = leftGridRef.value;
    if ($grid) {
      $grid.setCurrentRow(row);
    }
  },
  async menuClick({ menu, type, row }: { menu: any, type: any, row: any }) {
    if (menu.code == 'del') {
      const { id } = row;
      if (id) {
        ElMessageBox.confirm('你确定要删除吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'error',
        }).then(async () => {
          delData(id);
        });
      } else {
        if (leftGridRef.value) {
          leftGridRef.value.remove(row);

          isFinishInsert = true
        }
      }
    }
    if (menu.code == 'up' || menu.code == 'down') {
      const moveType = menu.code;
      moveData(moveType);
    }
  },
})

const changeLogData = ref()


// 改变变更记录状态
function changeLogStatus(type?: string) {
  if (type == 'close') {
    shouldLogShow.value = false
  } else {
    shouldLogShow.value = !shouldLogShow.value
  }
}

// 新增行
function handleAdd() {
  const customerRow = {
    status: versionStatus.DRAFT,
    companyVersion: '',
    remark: ''
  };
  nextTick(() => {
    if (isFinishInsert) {
      leftGridRef.value && leftGridRef.value.insertAt(customerRow);
      isFinishInsert = false
    }
  });
  leftGridRef.value
}

// 确认新增数据
async function insureAddorEdit(row: any) {
  if (row.id) {
    const id = row.id
    let params = {
      companyVersion: row.companyVersion,
      remark: row.remark,
    }
    const res = await updateBusinessVersion(id, params);

    if (res) {
      ElMessage.success('修改成功');
      isFinishInsert = true
      reflash()
    }
  } else {
    let params = {
      companyVersion: row.companyVersion,
      remark: row.remark,
    }
    const res = await addBusinessVersion(params)
    if (res) {
      ElMessage.success('添加成功');
      isFinishInsert = true
      reflash()
    }
  }
}

// 选择数据后的操作
function handleChoice(row: any) {
  orgInfoForm.id = row.id
  orgInfoForm.companyName = row.companyName
  orgInfoForm.unifiedSocialCreditCode = row.unifiedSocialCreditCode
  orgInfoForm.registeredAddress = row.registeredAddress
  orgInfoForm.companyLocation = row.companyLocation
  orgInfoForm.taxpayerType = row.taxpayerType
  orgInfoForm.businessPhone = row.businessPhone
  orgInfoForm.bankName = row.bankName
  orgInfoForm.bankAddress = row.bankAddress
  orgInfoForm.bankAccount = row.bankAccount
  orgInfoForm.status = row.status
  isChoiceOrg.value = true



  nextTick(() => {
    formEl.value.clearValidate();
  })
  // console.log(formEl.value)
  getChangeLog()
}
// 保存操作
async function handleSave() {
  const isValidate = await formEl.value.validate()
  if (!isValidate) return;
  const id = orgInfoForm.id
  let params = {
    companyName: orgInfoForm.companyName,
    unifiedSocialCreditCode: orgInfoForm.unifiedSocialCreditCode,
    registeredAddress: orgInfoForm.registeredAddress,
    companyLocation: orgInfoForm.companyLocation,
    taxpayerType: orgInfoForm.taxpayerType,
    businessPhone: orgInfoForm.businessPhone,
    bankName: orgInfoForm.bankName,
    bankAddress: orgInfoForm.bankAddress,
    bankAccount: orgInfoForm.bankAccount
  }
  const res = await saveBusinessInfo(id, params);
  reflash()
}
// 发布操作
async function handlePublish() {

  ElMessageBox.confirm(`你确定要${orgInfoForm.status === versionStatus.PUBLISHED ? '取消' : ''}发布吗?`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    const id = orgInfoForm.id
    if (orgInfoForm.status === versionStatus.PUBLISHED) {
      const res = await cancelPublishBusinessInfo(id)
      if (res) {
        ElMessage.success('取消发布成功')
      }
    } else {
      const res = await publishBusinessInfo(id)
      if (res) {
        ElMessage.success('发布成功')
      }
    }
    reflash();
  });
}
// 删除操作
async function delData(id: string) {
  const businessId = id ?? orgInfoForm.id
  const res = await delBusinessInfo(businessId)
  if (res) {
    reflash();
    ElMessage.success('删除成功');
  }
}
// 移动操作
type moveType = 'up' | 'down'
async function moveData(type: moveType) {
  const id = orgInfoForm.id
  let params = { id, moveType: type }
  const res = await moveBusinessInfo(params)
  if (res) {
    reflash();
  }
}
// 获取变更记录
async function getChangeLog() {



  const id = orgInfoForm.id
  const records = await getBusinessInfoChangeLogList(id);

  // 按 versionNumber 分组
  const grouped = records.reduce((acc: any, record: any) => {
    const version = record.versionNumber;
    if (!acc[version]) {
      acc[version] = [];
    }
    acc[version].push(record);
    return acc;
  }, {} as Record<string, typeof records>);

  // 转为期望格式
  const result = Object.entries(grouped).map(([versionNumber, data]: any) => {
    const time = data[0].createAt;
    const name = data[0].createByName;
    return ({
      versionNumber,
      time,
      name,
      data,
    })
  })
    .sort((a, b) => new Date(b.time).getTime() - new Date(a.time).getTime());


  changeLogData.value = result
}
// 刷新
async function reflash() {
  await getList();
  getChangeLog()
}
// 获取公司版本列表
async function getList() {
  leftGridOptions.loading = true;
  const res = await getBusinessInfoList()
  leftGridOptions.data = res
  leftGridOptions.loading = false;



  console.log(orgInfoForm.id, orgInfoForm.id)
  if (orgInfoForm.id && orgInfoForm.id !== '') {
    const item = res.find((v: any) => v.id == orgInfoForm.id);
    nextTick(() => {
      const $grid = leftGridRef.value;
      if ($grid) {
        $grid.setCurrentRow(item);
      }
    })
    handleChoice(item)
  }
}

onMounted(() => {
  getList();
})
</script>

<style scoped lang="scss">
.el-form-item {
  margin-bottom: 0;
}

.el-input__wrapper {
  border: none !important;
  box-shadow: none !important;
}

.el-input.is-focus .el-input__wrapper {
  border: none !important;
  box-shadow: none !important;
}


.drawer-slide-enter-active,
.drawer-slide-leave-active {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.3s;
}

.drawer-slide-enter-from,
.drawer-slide-leave-to {
  transform: translateX(100%);
  opacity: 0;
}

.drawer-slide-enter-to,
.drawer-slide-leave-from {
  transform: translateX(0);
  opacity: 1;
}
</style>
