<template>
  <div class="record-box" v-bind="$attrs">
    <ElDrawer
      v-model="drawerVisible"
      @close="handleDrawerClose"
      @open="openDrawer"
    >
      <template #title>
        <span
          class="text"
          :class="[showSideStatus === 'annex' ? 'annexText' : 'recordText']"
        >
          {{ showSideStatus === 'annex' ? '附件' : '变更记录' }}
        </span>
      </template>
      <!-- 变更记录 -->
      <ChangeRecord
        :records-list="records"
        v-if="showSideStatus === 'record'"
      />
      <!-- 附件 -->
      <div v-else>
        <BasicUpload
          drag
          :upload-url="`${apiURL}/ecost/file-manage/upload/`"
          v-model:file-list="filesList"
          :max-size-k-b="1024"
          :max-count="3"
          :multiple="false"
        />
      </div>
      <div class="side-box">
        <div class="side-item annex" @click="changeItem('annex')">附件</div>
        <div class="side-item record" @click="changeItem('record')">
          变更记录
        </div>
      </div>
    </ElDrawer>
  </div>
</template>

<script setup lang="ts">
import { defineEmits, defineProps, ref, watch } from 'vue';

import { useAppConfig } from '@vben/hooks';

import { ElDrawer } from 'element-plus';
import _ from 'lodash';

import { ChangeRecords } from '#/api/enterpriseCenter/enterpriseStandards/supplier';
import BasicUpload from '#/components/BasicUpload/index.vue';

import ChangeRecord from './components/changeRecord.vue';

const props = withDefaults(
  defineProps<{
    code: 'annex' | 'record';
    files: any;
    id: string;
    visible: boolean;
  }>(),
  {
    visible: false,
    code: 'annex',
    files: () => [],
    id: '',
  },
);
const emit = defineEmits<{
  (e: 'update:visible', visible: boolean): void;
  (e: 'update:code', code: string): void;
  (e: 'update:files', file: any): void;
  (e: 'refresh'): void;
}>();
const { apiURL } = useAppConfig(import.meta.env, import.meta.env.PROD);
const showSideStatus = ref(props.code);
function changeItem(code: string) {
  emit('update:code', code);
}

function handleDrawerClose() {
  emit('update:visible', false);
}
const filesList = ref([]);
watch(
  () => props.files,
  (val) => {
    if (!_.isEqual(val, filesList.value)) {
      filesList.value = [...(val || [])];
    }
  },
  {
    immediate: true,
    deep: true,
  },
);
watch(
  () => filesList.value,
  (newVal) => {
    if (!_.isEqual(newVal, props.files)) {
      emit('update:files', [...newVal]);
    }
  },
  { deep: true },
);

const records = ref([]); // 变更记录
async function openDrawer() {
  records.value = await ChangeRecords(props.id);
}

const drawerVisible = ref(props.visible);
watch(
  () => props.visible,
  (newValue) => {
    drawerVisible.value = newValue;
  },
);
watch(
  () => props.code,
  (newValue) => {
    showSideStatus.value = newValue;
  },
);
</script>

<style scoped lang="scss">
.record-box {
  position: relative;

  .text {
    font-size: 16px;
    font-weight: 500;
  }
  .annexText {
    color: #5ac37d;
  }
  .recordText {
    color: #3f90f8;
  }

  .side-box {
    position: absolute;
    width: 20px;
    left: 0;
    top: 80px;
    display: flex;
    align-items: center;
    flex-direction: column;

    .side-item {
      padding: 5px;
      display: flex;
      align-items: center;
      flex-direction: column;
      color: #ffffff;
      width: 100%;
      text-align: center;
      font-size: 12px;
      &:hover {
        cursor: pointer;
      }
    }
    .annex {
      background-color: #5ac37d;
      border-top-left-radius: 5px;
      border-top-right-radius: 5px;
    }
    .record {
      background-color: #3f90f8;
      border-bottom-left-radius: 5px;
      border-bottom-right-radius: 5px;
    }
  }
}
</style>
