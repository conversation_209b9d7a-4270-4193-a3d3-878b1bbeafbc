import { ref } from 'vue';
/**
 * 分列组件配置
 */
export const colPageProps = ref({
  leftCollapsedWidth: 3,
  leftCollapsible: true,
  leftMaxWidth: 30,
  leftMinWidth: 20,
  leftWidth: 30,
  resizable: true,
  rightWidth: 70,
  splitHandle: true,
  splitLine: true,
  autoContentHeight: true,
});

// 版本启用状态
export enum EnableStatus {
  DISABLED = 'DISABLED', //  已停用
  ENABLED = 'ENABLED', //  已启用
  NOT_ENABLED = 'NOT_ENABLED', //  未启用
}
// 版本启用状态TEXT
export enum EnableStatusText {
  DISABLED = '已停用',
  ENABLED = '已启用',
  NOT_ENABLED = '未启用',
}

// 材料类型枚举
export enum MaterialType {
  EXCLUSIVE_USE = 'EXCLUSIVE_USE', // / 增值税专用发票
  GENERAL_USE = 'GENERAL_USE', // / 增值税普通发票
}

// 材料字典类型枚举TEXT
export enum MaterialTypeText {
  EXCLUSIVE_USE = '增值税专用发票',
  GENERAL_USE = '增值税普通发票',
}

// 材料字典
export const materialDict = [
  {
    label: '增值税专用发票',
    value: MaterialType.EXCLUSIVE_USE,
  },
  {
    label: '增值税普通发票',
    value: MaterialType.GENERAL_USE,
  }
];
