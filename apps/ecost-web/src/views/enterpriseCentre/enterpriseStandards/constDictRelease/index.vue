<template>
  <ColPage v-bind="colPageProps" content-class="content">
    <template #left="{ isCollapsed, expand }">
      <div v-if="isCollapsed" @click="expand">
        <ElTooltip content="点击展开左侧">
          <ElButton circle round type="primary">
            <template #icon>
              <IconifyIcon class="text-2xl" icon="bi:arrow-right" />
            </template>
          </ElButton>
        </ElTooltip>
      </div>
      <!-- 左侧表格区域 -->
      <div :style="{ minWidth: '200px', overflow: 'hidden' }" v-else class="bg-card h-full rounded-lg border p-2 pb-20">
        <div class="flex-row items-center">
          <div class="flex items-center justify-between">
            <ElButton type="primary" size="small" @click="handleMaintenanceVersion">
              维护版本
            </ElButton>
            <ElSelect v-model="currentSelectVersion" size="small" placeholder="请选择费用字典版本" style="width: 200px"
              @change="getClassifyList" :fit-input-width="true">
              <ElOption v-for="item in versions" :key="item.id" :value="item.id"
                :label="`${item.name}（${EnableStatusText[item.status as keyof typeof EnableStatusText]}）`"
                :title="item.name" />
            </ElSelect>
          </div>
          <div class="mb-2 mt-2 flex items-center justify-between">
            <div>
              <ElButton :disabled="isDisableAddClassify" type="primary" size="small" @click="addNewClassify">
                新增分类
              </ElButton>
              <ElButton :disabled="isDisableAddClassify" type="primary" size="small" @click="addNewLowerLevelClassify">
                新增下级分类
              </ElButton>
            </div>
            <ElDropdown size="small" @command="handleDropDownItem">
              <ElButton plain type="primary" size="small">
                导入文件
                <IconifyIcon icon="bi:arrow-down" />
              </ElButton>
              <template #dropdown>
                <ElDropdownMenu>
                  <ElDropdownItem command="download"> 模版下载 </ElDropdownItem>
                </ElDropdownMenu>
              </template>
            </ElDropdown>
          </div>
        </div>
        <vxe-grid ref="leftGridRef" v-bind="leftGridOptions" v-on="leftGridMethod">
          <template #empty>
            <span>
              {{ currentSelectVersion ? '没有更多数据了！' : '请选择费用字典版本！' }}
            </span>
          </template>
        </vxe-grid>
      </div>
    </template>
    <div class="drawerBox bg-card ml-2 h-full rounded-lg border p-2" style="position: relative">
      <div class="mb-2 flex items-center">
        <ElButton :disabled="isDisabledAddDetailsBtn" type="primary" size="small" @click="addNewDetails">
          新增明细
        </ElButton>
        <ElInput v-model="searchStr" class="ml-10" size="small" placeholder="请输入名称" style="width: 250px" clearable
          @clear="clearSearch" @keyup.enter="searchStrValue" @blur="searchStrValue">
          <template #prefix>
            <ElIcon class="el-input__icon">
              <Search />
            </ElIcon>
          </template>
        </ElInput>
      </div>
      <vxe-grid ref="rightGridRef" v-bind="rightGridOptions" v-on="rigtGridMethod">
        <!--  业务成本科目名称 -->
        <template #businessCost="{ row }">
          <ElTreeSelect class="treeSelect-box" multiple collapse-tags :data="businessCostAccountTreeList"
            v-model="row.businessCostSubjectDetailsIds" show-checkbox check-strictly default-expand-all node-key="id"
            :props="{
              disabled: (data: any) => !data.isLeaf,
              label: 'name',
              children: 'children',
            }" filterable :teleported="false" :filter-node-method="filterNodeMethod" />
        </template>
        <template #businessCost_default="{ row }">
          <span>
            {{
              filterBusinessCostAccountLabel(row.businessCostSubjectDetailsIds)
            }}
          </span>
        </template>
      </vxe-grid>
    </div>
    <!-- 新增版本 -->
    <AddVersion v-if="versionDialog" v-model:visible="versionDialog" @refresh="refresh" />
  </ColPage>
</template>

<script setup lang="ts">
import { TreeBusinessCostCategoryDetails } from '#/api/enterpriseCenter/enterpriseStandards/businessCostSubject';
import {
  AddCostDicCategory,
  AddCostDicDetail,
  DeleteCostDicCategory,
  DeleteCostDicDetail,
  ListCostDicCategory,
  ListCostDicDetail,
  ListCostDicVersion,
  MoveCostDicCategory,
  MoveCostDicDetail,
  QueryCostDicDetail,
  UpdateCostDicCategory,
  UpdateCostDicDetail
} from '#/api/enterpriseCenter/enterpriseStandards/costDictRelease';
import { downloadLocalFile } from '#/utils';
import { getNamesFromTreeByIds } from '#/utils/common';
import { Search } from '@element-plus/icons-vue';
import { ColPage } from '@vben/common-ui';
import { IconifyIcon } from '@vben/icons';
import {
  ElButton,
  ElDropdown,
  ElDropdownItem,
  ElDropdownMenu,
  ElIcon,
  ElInput,
  ElMessage,
  ElMessageBox,
  ElOption,
  ElSelect,
  ElTooltip,
  ElTreeSelect
} from 'element-plus';
import _ from 'lodash';
import { computed, nextTick, onMounted, reactive, ref } from 'vue';
import AddVersion from './components/AddVersion.vue';
import {
  colPageProps,
  EnableStatus,
  EnableStatusText
} from './data';



// 版本数据
const currentSelectVersion = ref('');
const versions = ref();
const versionDialog = ref(false); // 维护版本
function handleMaintenanceVersion() {
  versionDialog.value = true;
}
async function getVersionList() {
  // 获取版本数据
  versions.value = await ListCostDicVersion();
}
const curVersionInfo = ref(); // 版本切换
const staticData = {
  // 内置数据
  id: '100',
  name: '全部',
  code: 'All',
  versions: 'All',
  parentId: null,
  remark: '内置节点',
  isActive: true,
  type: 'All',
};

function handleDropDownItem(command: string) {
  if (command === 'download') {
    // TODO 模版下载
    downloadLocalFile('/file/费用字典发布.xlsx', '费用字典发布.xlsx');
  }
}
// 左侧表格
const leftGridRef = ref();
const leftCurrent = ref(); // 左侧选中数据
const businessCostAccountTreeList = ref([]);// 业务成本科目树
const leftGridOptions = reactive<any>({
  // 配置数据
  size: 'mini',
  border: true,
  keepSource: true,
  align: 'center',
  showOverflow: true,
  columnConfig: {
    resizable: true,
  },
  tooltipConfig: {
    showAll: true,
  },
  rowDragConfig: {
    isCrossDrag: true,
  },
  rowConfig: {
    isCurrent: true,
    // isHover: true,
  },
  treeConfig: {
    rowField: 'id',
    parentField: 'parentId',
    transform: true,
    expandAll: true,
  },
  editConfig: {
    trigger: 'dblclick',
    mode: 'cell',
    showStatus: true,
    beforeEditMethod({ row }: { row: any }) {
      // 版本数据已启用无法进行编辑
      if (
        curVersionInfo.value &&
        curVersionInfo.value.status === EnableStatus.ENABLED
      ) {
        return false;
      }
      if (row.id === '100') {
        return false;
      }
      // 弃用的数据无法编辑
      if (!row.isActive) {
        return false;
      }
      return true;
    },
  },
  rowClassName: ({ row }: any) => {
    if (row.id && row.id === rightCurrent.value?.costDictionaryCategoryId) {
      return 'details-row-click-color';
    }
    // 禁用数据标识
    if (!row.isActive) {
      return 'details-row-click-disable-color';
    }
    return null;
  },
  editRules: {
    name: [{ required: true, message: '名称不得为空！' }],
    code: [{ required: true, message: '编码不得为空！' }],
  },
  columns: [
    {
      treeNode: true,
      field: 'code',
      title: '编码',
      editRender: {
        name: 'VxeInput',
        props: {
          placeholder: '请输入编码',
        },
      },
    },
    {
      field: 'name',
      title: '名称',
      editRender: {
        name: 'VxeInput',
        props: {
          placeholder: '请输入名称',
        },
      },
    },
    {
      field: 'remark',
      title: '备注',
      editRender: {
        name: 'VxeInput',
        props: {
          placeholder: '请输入备注',
        },
      },
    },
  ],
  menuConfig: {
    body: {
      options: [
        [
          {
            code: 'DELETE_ROW',
            name: '删除行',
            disabled: false,
            prefixConfig: { icon: 'vxe-icon-delete-fill' },
          },
          {
            code: 'MOVE_UP',
            name: '上移',
            disabled: false,
            prefixConfig: { icon: 'vxe-icon-arrows-up' },
          },
          {
            code: 'MOVE_DOWN',
            name: '下移',
            disabled: false,
            prefixConfig: { icon: 'vxe-icon-arrows-down' },
          },
          {
            code: 'DISCARD',
            name: '废弃',
            disabled: false,
            prefixConfig: { icon: 'vxe-icon-warning-triangle' },
          },
          {
            code: 'ENABLED',
            name: '启用',
            disabled: false,
            prefixConfig: { icon: 'vxe-icon-success-circle' },
          },
        ],
      ],
    },
    visibleMethod: ({ options, row }: any) => {
      // 1. 内置节点状态控制
      if (row.id === '100') {
        options[0].forEach((item: any) => {
          item.disabled = true;
        });
        return true;
      }
      // 废弃&启用数据控制
      const isActive = row.isActive;
      options[0].forEach((item: any) => {
        switch (item.code) {
          case 'DISCARD': {
            item.disabled = !isActive;
            break;
          }
          case 'ENABLED': {
            item.disabled = isActive;
            break;
          }
          // 其他按钮始终可用
          default: {
            item.disabled = false;
          }
        }
      });

      return true;
    },
  },
  data: [],
});
const leftGridMethod = {
  async cellClick({ row }: any) {
    leftCurrent.value = row;
    // 获取明细数据
    await getDetailsList(currentSelectVersion.value, row.id);
    rightCurrent.value = {};
    searchStr.value = '';
  },
  async menuClick({ menu }: any) {
    const currentRow = leftCurrent.value;
    switch (menu.code) {
      case 'DELETE_ROW': {
        if (currentRow.id) {
          ElMessageBox.confirm('确定删除该数据？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }).then(async () => {
            // 存在下级分类无法删除
            const isExist = leftGridOptions.data.find(
              (item: any) => item.parentId === currentRow.id,
            );
            if (isExist)
              return ElMessage.warning('当前数据存在下级分类，无法删除！');
            const res = await DeleteCostDicCategory(currentRow.id);
            if (res) {
              ElMessage.success('操作成功！');
            }
            await getClassifyList(currentSelectVersion.value);
          });
        } else {
          // id 不存在临时数据删除
          return (leftGridOptions.data = leftGridOptions.data.filter(
            (item: any) => item.id,
          ));
        }
        nextTick(() => {
          leftGridRef.value.setAllTreeExpand(true);
        });

        break;
      }
      case 'DISCARD': {
        // 废弃
        ElMessageBox.confirm('是否废弃该数据？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }).then(async () => {
          const res = await UpdateCostDicCategory(currentRow.id, {
            isActive: false,
            name: leftCurrent.value.name,
            code: leftCurrent.value.code,
            parentId: leftCurrent.value.parentId,
            costDictionaryVersionId: leftCurrent.value.versionId,
            // remark: leftCurrent.value.remark,
            // type: leftCurrent.value.type,
          });
          if (res) ElMessage.success('操作成功！');
          await getClassifyList(currentSelectVersion.value);
        });
        break;
      }
      case 'ENABLED': {
        // 启用
        ElMessageBox.confirm('确定启用该数据？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }).then(async () => {
          const res = await UpdateCostDicCategory(currentRow.id, {
            isActive: true,
            name: leftCurrent.value.name,
            code: leftCurrent.value.code,
            parentId: leftCurrent.value.parentId,
            costDictionaryVersionId: leftCurrent.value.versionId,
            // type: leftCurrent.value.type,
            // remark: leftCurrent.value.remark,
          });
          if (res) ElMessage.success('操作成功！');
          await getClassifyList(currentSelectVersion.value);
        });
        break;
      }
      case 'MOVE_DOWN': {
        await leftGridRef.value.moveRowTo(leftCurrent.value, 1);
        await MoveCostDicCategory(leftCurrent.value.id, 'down');
        await getClassifyList(currentSelectVersion.value);
        break;
      }
      case 'MOVE_UP': {
        await leftGridRef.value.moveRowTo(leftCurrent.value, -1);
        await MoveCostDicCategory(leftCurrent.value.id, 'up');
        await getClassifyList(currentSelectVersion.value);
        break;
      }
    }
  },
  async editClosed({ row }: any) {
    if (leftGridRef.value) {
      const errMsg = await leftGridRef.value.validate(row);
      const isUpdateRow = leftGridRef.value.isUpdateByRow(row);
      if (!errMsg) {
        let res = null;
        if (row.id) {
          // 数据是否发生变化
          if (isUpdateRow) {
            // 编辑
            res = await UpdateCostDicCategory(row.id, {
              name: row.name,
              code: row.code,
              remark: row.remark,
              parentId: row.parentId,
              type: row.type,
              costDictionaryVersionId: row.versionId,
            });
          }
        } else {
          // 新增
          res = await AddCostDicCategory({
            name: row.name,
            code: row.code,
            remark: row.remark,
            parentId: row.parentId,
            type: row.type,
            costDictionaryVersionId: row.versionId,
          });
        }
        if (res) ElMessage.success('操作成功！');
        await getClassifyList(currentSelectVersion.value);
      }
    }
  }
}
async function getClassifyList(val: string) { // 获取分类数据
  curVersionInfo.value = versions.value.find((item: any) => item.id === val);
  const arr = await ListCostDicCategory(val);
  leftGridOptions.data = arr || [];
  leftGridOptions.data.unshift(staticData)

  // 切换版本获取业务成本科目树
  if (curVersionInfo.value.businessCostSubjectVersionId) {
    await getBusinessCostAccount(
      curVersionInfo.value.businessCostSubjectVersionId,
    );
  }
  // 数据至空
  // leftCurrent.value = {};
  // rightCurrent.value = {};
  nextTick(() => {
    // 默认选中第一项目
    leftGridRef.value.setCurrentRow(leftGridOptions.data[0])
    leftGridMethod.cellClick({ row: leftGridOptions.data[0] })

    leftGridRef.value.setAllTreeExpand(true);
  });
}
async function addNewClassify() {// 新增分类
  if (!currentSelectVersion.value) return ElMessage.warning('请选择业务版本！');
  const errMsg = await leftGridRef.value.validate(true);
  if (errMsg) {
    return ElMessage.warning('请完善表格数据！');
  }
  const newClassify = {
    name: '',
    code: '',
    remark: '',
    parentId: leftCurrent.value.parentId,
    versionId: currentSelectVersion.value,
    isActive: true,
  };
  await nextTick(() => {
    leftGridRef.value &&
      leftGridRef.value.insertNextAt(newClassify, leftCurrent.value, -1);
    leftGridRef.value.validate(leftCurrent.value);
  });
}
async function addNewLowerLevelClassify() { // 新增下级分类
  if (!currentSelectVersion.value) return ElMessage.warning('请选择业务版本！');
  // 点击第全部行 return
  if (leftCurrent.value.id === '100')
    return ElMessage.warning('当前节点无法新增下级分类！');
  if (rightGridOptions.data.length > 0)
    return ElMessage.warning('当前节点下存在明细数据，无法新增下级分类！');
  const errMsg = await leftGridRef.value.validate(true);
  if (errMsg) {
    return ElMessage.warning('请完善表格数据！');
  }
  const newLowerLevelClassify = {
    name: '',
    code: '',
    remark: '',
    parentId: leftCurrent.value.id,
    versionId: currentSelectVersion.value,
    isActive: true,
  };
  await nextTick(() => {
    leftGridRef.value &&
      leftGridRef.value.insertChildAt(
        newLowerLevelClassify,
        leftCurrent.value,
        -1,
      );
    leftGridRef.value.validate(leftCurrent.value);
  });
}
const isDisableAddClassify = computed(() => { // 新增分类与新增下级分类按钮禁用状态
  // 1.版本已启用按钮禁用
  // 2.未选择分类数据禁用
  // 3.选中的分类数据id为空禁用 - id为空意味着是新增数据
  // 4.分类已被弃用禁用
  return currentSelectVersion.value &&
    curVersionInfo.value.status !== EnableStatus.ENABLED
    ? _.isEmpty(leftCurrent.value) ||
    !leftCurrent.value.id ||
    leftCurrent.value.isActive !== true
    : true;
});

// 右侧表格
const rightGridRef = ref();
const rightCurrent = ref(); // 右侧选中数据
const rightGridOptions = reactive<any>({
  size: 'mini',
  height: '100%',
  border: true,
  keepSource: true,
  align: 'center',
  showOverflow: true,
  rowDragConfig: {
    isCrossDrag: true,
  },
  editConfig: {
    trigger: 'dblclick',
    mode: 'cell',
    showStatus: true,
    showIcon: false,
    beforeEditMethod({ row }: any) {
      // 版本数据已启用无法进行编辑
      if (
        curVersionInfo.value &&
        curVersionInfo.value.status === EnableStatus.ENABLED
      ) {
        return false;
      }
      // 废弃数据不可编辑
      if (!row.isActive) {
        return false;
      }
      return true;
    },
  },
  columnConfig: {
    resizable: true,
  },
  rowConfig: {
    isCurrent: true,
    // isHover: true,
  },
  rowClassName: ({ row }: any) => {
    // 禁用数据标识
    if (!row.isActive) {
      return 'details-row-click-disable-color';
    }
    return null;
  },
  menuConfig: {
    body: {
      options: [
        [
          {
            code: 'DELETE_ROW',
            name: '删除行',
            prefixConfig: { icon: 'vxe-icon-delete-fill' },
          },
          {
            code: 'MOVE_UP',
            name: '上移',
            prefixConfig: { icon: 'vxe-icon-arrows-up' },
          },
          {
            code: 'MOVE_DOWN',
            name: '下移',
            prefixConfig: { icon: 'vxe-icon-arrows-down' },
          },
          {
            code: 'DISCARD',
            name: '废弃',
            prefixConfig: { icon: 'vxe-icon-warning-triangle' },
          },
          {
            code: 'ENABLED',
            name: '启用',
            prefixConfig: { icon: 'vxe-icon-success-circle' },
          },
        ],
      ],
    },
    visibleMethod: ({ options, row, rowIndex }: any) => {
      const dataLength = rightGridOptions.data.length;
      const isActive = row?.isActive;

      // 特殊处理：只有一条数据，两个移动按钮都禁用
      const isOnlyOne = dataLength === 1;

      const isFirst = rowIndex === 0;
      const isLast = rowIndex === dataLength - 1;

      options[0].forEach((item: any) => {
        switch (item.code) {
          case 'DISCARD': {
            item.disabled = !isActive;
            break;
          }
          case 'ENABLED': {
            item.disabled = isActive;
            break;
          }
          case 'MOVE_DOWN': {
            item.disabled = isOnlyOne || isLast;
            break;
          }
          case 'MOVE_UP': {
            item.disabled = isOnlyOne || isFirst;
            break;
          }
          default: {
            item.disabled = false;
          }
        }
      });

      return true;
    },
  },
  editRules: {
    name: [{ required: true, message: '名称不得为空！' }],
    code: [{ required: true, message: '编码不得为空！' }],
    // type: [{ required: true, message: '规格型号不得为空！' }],
  },
  columns: [
    { type: 'seq', width: 50, title: '序号' },
    {
      title: '编码',
      field: 'code',
      filters: [{ data: '' }],
      filterMethod({
        option,
        row,
        column,
      }: {
        column: any;
        option: any;
        row: any;
      }) {
        if (option.data) {
          return `${row[column.field]}`.includes(option.data);
        }
        return true;
      },
      filterRender: {
        name: 'FilterInput',
      },
      editRender: {
        name: 'VxeInput',
        props: {
          placeholder: '请输入编码',
        },
      },
    },
    {
      width: 150,
      field: 'name',
      title: '名称',
      editRender: {
        name: 'VxeInput',
      },
      filters: [{ data: '' }],
      filterMethod({
        option,
        row,
        column,
      }: {
        column: any;
        option: any;
        row: any;
      }) {
        if (option.data) {
          return `${row[column.field]}`.includes(option.data);
        }
        return true;
      },
      filterRender: {
        name: 'FilterInput',
      },
    },

    {
      field: 'remark',
      title: '备注',
      editRender: {
        name: 'VxeInput',
        props: {
          placeholder: '请输入备注',
        },
      },
      filters: [{ data: '' }],
      filterMethod({
        option,
        row,
        column,
      }: {
        column: any;
        option: any;
        row: any;
      }) {
        if (option.data) {
          return `${row[column.field]}`.includes(option.data);
        }
        return true;
      },
      filterRender: {
        name: 'FilterInput',
      },
    },
    {
      field: 'businessCostSubjectDetailsIds',
      title: '业务成本科目名称',
      editRender: {},
      slots: {
        edit: 'businessCost',
        default: 'businessCost_default',
      },
    },
    {
      field: 'accountExplanation',
      title: '核算说明',
      editRender: {
        name: 'VxeInput',
        props: {
          placeholder: '请输入核算说明',
        },
      },
      filters: [{ data: '' }],
      filterMethod({
        option,
        row,
        column,
      }: {
        column: any;
        option: any;
        row: any;
      }) {
        if (option.data) {
          return `${row[column.field]}`.includes(option.data);
        }
        return true;
      },
      filterRender: {
        name: 'FilterInput',
      },
    },
  ],
  data: [],
});
const rigtGridMethod = {
  // 右侧方法
  async cellClick({ row }: any) {
    rightCurrent.value = row;
    const classifyCurRow = leftGridOptions.data.find((item: any) => item.id === row.businessCostSubjectCategoryId,);
    // 定位 & 高亮
    if (leftGridRef.value && leftGridOptions.data) {
      leftGridRef.value.setCurrentRow(classifyCurRow);
      leftGridRef.value.scrollToRow(classifyCurRow);
    }
  },
  async editClosed({ row }: any) {
    if (!rightGridRef.value) return;
    // 如果数据没有通过校验，直接 return
    const errMsg = await rightGridRef.value.validate(row);
    if (errMsg) return;
    if (row.id) {
      if (rightGridRef.value.isUpdateByRow(row)) {
        const res = await UpdateCostDicDetail(row.id, {
          costDictionaryVersionId: row.costDictionaryVersionId, // 版本id
          costDictionaryCategoryId: row.costDictionaryCategoryId, // 分类id
          code: row.code, // 编码
          name: row.name, // 机械字典名称

          remark: row.remark, //备注
          businessCostSubjectDetailsIds: row.businessCostSubjectDetailsIds, // 业务成本科目明细 id
          accountExplanation: row.accountExplanation,// 核算说明
        });
        if (res) ElMessage.success('操作成功！');
      }
    } else {
      const res = await AddCostDicDetail({
        costDictionaryVersionId: row.costDictionaryVersionId, // 版本id
        costDictionaryCategoryId: row.costDictionaryCategoryId, // 分类id
        code: row.code, // 编码
        name: row.name, // 机械字典名称

        remark: row.remark, //备注
        businessCostSubjectDetailsIds: row.businessCostSubjectDetailsIds, // 业务成本科目明细 id
        accountExplanation: row.accountExplanation,// 核算说明
      });
      if (res) ElMessage.success('操作成功！');
    }

    await getDetailsList(currentSelectVersion.value, leftCurrent.value.id);
  },
  async menuClick({ menu }: any) {
    const currentRow = rightCurrent.value;
    switch (menu.code) {
      case 'DELETE_ROW': {
        if (currentRow.id) {
          ElMessageBox.confirm('确定删除该数据？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }).then(async () => {
            const res = await DeleteCostDicDetail(
              rightCurrent.value.id,
            );
            if (res) ElMessage.success('操作成功！');
            await getDetailsList(
              currentSelectVersion.value,
              leftCurrent.value.id,
            );
          });
        } else {
          // 临时数据删除
          return (rightGridOptions.data = rightGridOptions.data.filter(
            (item: any) => item.id,
          ));
        }
        break;
      }
      case 'DISCARD': {
        // 废弃
        ElMessageBox.confirm('是否废弃该数据？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }).then(async () => {
          const res = await UpdateCostDicDetail(currentRow.id, {
            isActive: false,
            costDictionaryVersionId:
              rightCurrent.value.costDictionaryVersionId, // 版本id
            costDictionaryCategoryId:
              rightCurrent.value.costDictionaryCategoryId, // 分类id
            code: rightCurrent.value.code, // 编码
            name: rightCurrent.value.name, // 机械字典名称

            remark: rightCurrent.value.remark, //备注
            businessCostSubjectDetailsIds: rightCurrent.value.businessCostSubjectDetailsIds, // 业务成本科目明细 id
            accountExplanation: rightCurrent.value.accountExplanation,// 核算说明
          });
          if (res) ElMessage.success('操作成功！');
          await getDetailsList(currentSelectVersion.value, leftCurrent.value.id);
        });
        break;
      }
      case 'ENABLED': {
        // 启用
        ElMessageBox.confirm('确定启用该数据？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }).then(async () => {
          const res = await UpdateCostDicDetail(currentRow.id, {
            isActive: true,
            costDictionaryVersionId:
              rightCurrent.value.costDictionaryVersionId, // 版本id
            costDictionaryCategoryId:
              rightCurrent.value.costDictionaryCategoryId, // 分类id
            code: rightCurrent.value.code, // 编码
            name: rightCurrent.value.name, // 机械字典名称

            remark: rightCurrent.value.remark, //备注
            businessCostSubjectDetailsIds: rightCurrent.value.businessCostSubjectDetailsIds, // 业务成本科目明细 id
            accountExplanation: rightCurrent.value.accountExplanation,// 核算说明
          });
          if (res) ElMessage.success('操作成功！');
          await getDetailsList(currentSelectVersion.value, leftCurrent.value.id);
        });
        break;
      }
      case 'MOVE_DOWN': {
        await rightGridRef.value.moveRowTo(rightCurrent.value, 1);
        await MoveCostDicDetail(rightCurrent.value.id, 'down');
        await getDetailsList(currentSelectVersion.value, leftCurrent.value.id);
        break;
      }
      case 'MOVE_UP': {
        await rightGridRef.value.moveRowTo(rightCurrent.value, -1);
        await MoveCostDicDetail(rightCurrent.value.id, 'up');
        await getDetailsList(currentSelectVersion.value, leftCurrent.value.id);
        break;
      }
    }
  },
}
const searchStr = ref(''); // 搜索数据
async function searchStrValue() {
  if (!currentSelectVersion.value) {
    return ElMessage.warning('请选择费用字典版本！');
  }
  rightGridOptions.data = await QueryCostDicDetail({
    costDictionaryVersionId: currentSelectVersion.value,
    name: searchStr.value,
  });
}
async function clearSearch() {
  await getDetailsList(currentSelectVersion.value, '100');
  rightCurrent.value = {};
  const allRow = leftGridOptions.data.find((item: any) => item.id === '100');
  leftGridRef.value && leftGridRef.value.setCurrentRow(allRow);
}
async function addNewDetails() {// 新增明细
  const errMsg = await rightGridRef.value.validate(true);
  if (errMsg) {
    return ElMessage.warning('请完善表格数据！');
  }
  const newDetails = {
    costDictionaryVersionId: currentSelectVersion.value, // 版本id
    costDictionaryCategoryId: leftCurrent.value.id, // 分类id
    code: '', // 编码
    name: '', // 机械字典名称

    remark: '',// 备注
    businessCostSubjectDetailsIds: [], // 业务成本科目明细 id
    accountExplanation: '', // 核算说明
    isActive: true,
  };
  if (rightGridRef.value) {
    rightGridRef.value.insertAt(newDetails, -1);
  }
}

async function getDetailsList(versionId: string, categoryId: string) { // 获取明细数据
  rightGridOptions.data = await ListCostDicDetail({
    costDictionaryVersionId: versionId,
    costDictionaryCategoryId: categoryId,
  });
}
const isDisabledAddDetailsBtn = computed(() => { // 新增明细按钮禁用状态
  // 版本启用禁用
  if (
    currentSelectVersion.value &&
    curVersionInfo.value.status === EnableStatus.ENABLED
  ) {
    return true;
  }
  // 如果分类废弃，无法新增明细
  if (!leftCurrent.value?.isActive) {
    return true;
  }
  // 是否叶子节点，叶子节点才可以新增明细
  if (!leftCurrent.value.isLeaf) {
    return true;
  }
  if (_.isEmpty(leftCurrent.value)) {
    return true;
  } else {
    return leftCurrent.value.id ? leftCurrent.value.id === '100' : true;
  }
});


// 获取业务成本科目树
async function getBusinessCostAccount(businessVersionId: string) {
  // TODO 根据版本id获取业务成本科目树
  businessCostAccountTreeList.value =
    await TreeBusinessCostCategoryDetails(businessVersionId);
}
// 业务成本科目树选中
function filterBusinessCostAccountLabel(ids: [string]) {
  return getNamesFromTreeByIds(businessCostAccountTreeList.value, ids);
}

const filterNodeMethod = (value: string, data: any) =>
  data.name.includes(value);

// 刷新接口数据
async function refresh() {
  await getVersionList();
  // 重新获取分类列表
  if (currentSelectVersion.value) {
    await getClassifyList(currentSelectVersion.value)
    // if (leftCurrent.value) {
    //   const item = leftGridOptions.data.find(v => v.id == leftCurrent.value.id)
    //   nextTick(() => {
    //     leftGridRef.value.setCurrentRow(item);
    //   })
    // }
  }
}

// 初始化
async function init() {
  await getVersionList();
  const activeItem = versions.value.find((v: any) => v.status === EnableStatus.ENABLED)
  if (activeItem) {
    currentSelectVersion.value = activeItem.id;
    await getClassifyList(currentSelectVersion.value);
  }
}

onMounted(() => {
  init();
});
</script>
<style lang="scss" scoped>
::v-deep(.vxe-grid .vxe-body--row.details-row-click-color) {
  background-color: #f6e692;
  color: #6a6b6e;
}

::v-deep(.vxe-grid .vxe-body--row.details-row-click-disable-color) {
  background-color: #b9bcc5;
  color: #6a6b6e;
}

.content {
  height: 100%;
}

.showBox {
  width: 95.5%;
  position: absolute;
  left: 20px;
  bottom: -40px;

  :deep(.el-card__header) {
    padding: 0;
  }

  .card-header {
    padding: 6px;
    font-size: 14px;
    display: flex;
    justify-content: space-between;

    .icon {
      font-size: 15px;
      cursor: pointer;
    }
  }
}

.drawer-box {
  position: absolute;
  bottom: 10px;
  left: 20px;
  width: 95.5%;

  :deep(.el-card__body) {
    padding: 10px;
  }

  :deep(.el-card__header) {
    padding: 0;
  }

  .card-header {
    padding: 6px;
    font-size: 14px;
    display: flex;
    justify-content: space-between;

    .icon {
      font-size: 15px;
      cursor: pointer;
    }
  }
}

.slide-up-enter-from {
  transform: translateY(100%);
  opacity: 0;
}

.slide-up-enter-active {
  transition:
    transform 0.3s ease,
    opacity 0.3s ease;
}

.slide-up-enter-to {
  transform: translateY(0);
  opacity: 1;
}

.selectPopup {
  z-index: 999999 !important;
  background-color: #ff0066 !important;
}

:deep(.treeSelect-box) {
  .el-select__wrapper {
    height: 33px !important;

    .el-select__selection {
      height: 25px !important;
    }
  }
}
</style>
