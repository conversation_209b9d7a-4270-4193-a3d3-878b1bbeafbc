<template>
  <Page class="ml-4 mt-4 rounded bg-white">
    <vxe-grid ref="tableRef" :loading="tableLoading" v-bind="tableOptions" v-on="gridEvents">
      <template #top>
        <div class="flex items-center gap-4">
          <div class="mb-2">
            <ElButton class="w-20" type="primary" size="small" :disabled="!couldUpload" @click="uploadContract">
              上传范本
            </ElButton>
          </div>
          <div class="mb-2">
            <ElButton class="w-20" :disabled="!couldPublish" type="primary" size="small">
              发布
            </ElButton>
          </div>
        </div>
      </template>

      <template #versionStatus="{ row, $rowIndex }">
        <ElTag v-if="row.versionStatus" :type="row.versionStatus === versionStatus.PUBLISHED
          ? 'success'
          : row.versionStatus === versionStatus.NOUSEING
            ? 'danger'
            : 'primary'
          ">
          {{ getStatusLabel(row.versionStatus) }}
        </ElTag>
      </template>
      <template #seq="{ row, $rowIndex }">
        <div>{{ $rowIndex + 1 }}</div>
      </template>
      <template #name="{ row, $rowIndex }">
        <div>
          <ElButton size="small" v-if="row.parentId" type="primary" link @click="editContractTemplate({ row })">{{
            row.name
          }}
          </ElButton>
          <span v-else>{{ row.name }}</span>
        </div>
      </template>


      <template #cnSeq="{ row, $rowIndex }">
        <div>{{ row.parentId ? row.sort : numberToChinese(row.sort) }}</div>
      </template>
      <template #isMatching="{ row, $rowIndex }">
        <div>
          <ElButton v-if="row.parentId" size="small" :type="row.isMatching === '全部匹配' ? 'primary' : 'default'" link>{{
            row.isMatching }}
          </ElButton>
        </div>
      </template>
      <template #isMandatory="{ row, $rowIndex }">
        <ElButton v-if="row.parentId" size="small" :type="row.isMandatory === '有' ? 'primary' : 'default'" link>{{
          row.isMandatory }}
        </ElButton>
      </template>
      <template #compileDate="{ row }">
        <div>{{ dayjs(row.compileDate).format('YYYY-MM-DD hh:mm:ss') }}</div>
      </template>
      <template #referNums="{ row }">
        <div>{{ row.referNums }}</div>
      </template>
    </vxe-grid>
    <ContractWord ref="contractWordEl" v-model:visible="drawerVisible" :formData="addOrEditForm" :dcoxFile="dcoxFile"
      :fieldRule="fieldRule" :mandatoryTerm="mandatoryTerm" @submit="submit" @refresh="refreshTable"></ContractWord>

    <input ref="fileEl" type="file" class="hidden"
      accept=".docx,application/vnd.openxmlformats-officedocument.wordprocessingml.document"
      @change="handleFileChange" />
  </Page>
</template>

<script lang="ts" setup>
import { fileDownload, fileUpload } from '#/api/commonApi';
import {
  addContractStandard,
  delContractStandard,
  editContractStandard,
  getContractStandardList,
  moveContractStandard
} from '#/api/enterpriseCenter/contractTemplate/contractTemplate';
import ContractWord from '#/components/ContractEditor/index.vue';
import { numberToChinese } from '#/utils/common';
import { Page } from '@vben/common-ui';
import dayjs from 'dayjs';
import { ElButton, ElMessage, ElMessageBox, ElTag } from 'element-plus';
import { onBeforeMount, reactive, ref } from 'vue';
import type { VxeGridProps } from 'vxe-table';
import { addMandatoryTerm, getStandardFieldRuleList, getMandatoryTermList } from '#/api/enterpriseCenter/contractTemplate/contractTemplate'

enum versionStatus {
  PUBLISHED = 'PUBLISHED', //  发布
  UNPUBLISHED = 'UNPUBLISHED', // 未发布
  NOUSEING = 'NOUSEING' // 停用
}

const contractWordEl = ref();
const drawerVisible = ref(false); // 合同范本侧抽屉
const tableLoading = ref(false);
const couldUpload = ref(false);
const couldPublish = ref(false);

const fileEl = ref();
const tableRef = ref();
const addOrEditForm = ref({
  id: '',
  classify: '',
  name: '',
  remark: '',
  filePath: '',
  fileSize: '',
  fileSuffix: '',
});
const fieldRule = ref([])
const mandatoryTerm = ref([])

const dcoxFile = ref<File | undefined>(undefined);
const currentItem = ref();




const columns = [
  {
    file: 'seq',
    // type: 'seq',
    title: '',
    width: '60',
    slots: {
      default: 'seq',
    },
    treeNode: true
  },
  {
    field: 'cnSeq',
    title: '序号',
    width: '60',
    slots: {
      default: 'cnSeq',
    },
  },
  {
    field: 'name',
    title: '合同类型/范本名称',
    width: '150',
    slots: {
      default: 'name',
    },
  },
  {
    field: 'remark',
    title: '范本说明',
    width: '280',
  },
  {
    field: 'compileDate',
    title: '编制日期',
    width: '180',
    slots: {
      default: 'compileDate',
    },
  },
  {
    field: 'compileBy',
    title: '编制人',
    width: '180',
  },
  {
    field: 'isMatching',
    title: '字段匹配',
    width: '120',
    slots: {
      default: 'isMatching',
    },
  },
  {
    field: 'isMandatory',
    title: '强制性条款',
    width: '120',
    slots: {
      default: 'isMandatory',
    },
  },
  {
    field: 'versionStatus',
    title: '发布状态',
    width: '180',
    slots: {
      default: 'versionStatus',
    },
  },
  {
    field: 'referNums',
    title: '被引用数',
    minWitdh: '100',
    slots: {
      default: 'referNums',
    },
  },
];

const getStatusLabel = (status: versionStatus) => {
  const statusMap = {
    [versionStatus.PUBLISHED]: '发布',
    [versionStatus.UNPUBLISHED]: '未发布',
    [versionStatus.NOUSEING]: '停用',
  };
  return statusMap[status] || '未知状态';
};

const tableOptions = reactive<VxeGridProps>({
  size: 'mini',
  height: 800,
  border: true,
  align: 'center',
  showOverflow: true,
  keepSource: true,
  headerRowClassName: 'bg-[skyblue]',
  rowClassName: ({ row }: { row: { parentId: string | null } }) => {
    if (row.parentId) {
      return '';
    } else {
      return 'bg-gray-100';
    }
  },
  columnConfig: {
    resizable: true,
  },
  treeConfig: {
    rowField: 'id',
    parentField: 'parentId',
    transform: true,
    expandAll: true,
  },
  rowConfig: {
    isCurrent: true,
  },
  menuConfig: {
    body: {
      options: [
        [{ code: 'del', name: '删除', visible: true, disabled: false }],
        [{ code: 'up', name: '上移', visible: true, disabled: false }],
        [{ code: 'down', name: '下移', visible: true, disabled: false }],
      ],
    },
  },
  checkboxConfig: {
    labelField: 'name',
    highlight: true,
  },
  columns,
  data: [],
});

// 表格grid全部事件
const gridEvents = {
  cellClick({ row }: { row: any }) {
    couldUpload.value = true;
    couldPublish.value = true;
  },
  cellMenu({ row }: { row: any }) {
    couldUpload.value = true;
    couldPublish.value = true;
    const $grid = tableRef.value;
    if ($grid) {
      $grid.setCurrentRow(row);
    }
  },
  async menuClick({ menu, type, row }: { menu: any, type: any, row: any }) {
    if (menu.code == 'del') {
      const { id } = row;
      ElMessageBox.confirm('你确定要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'error',
      }).then(async () => {
        const res = await delContractStandard(id);
        if (res) {
          refreshTable();
          ElMessage.success('删除成功');
        }
      });
    }
    if (menu.code == 'up' || menu.code == 'down') {
      const { id } = row;
      const moveType = menu.code;
      let params = {
        id,
        moveType,
      };
      const res = await moveContractStandard(params);
      if (res) {
        refreshTable();
      }
    }
  },
};

// 点击新增范本
const uploadContract = () => {
  fileEl.value.value = ''; // 清空文件选择
  dcoxFile.value = undefined; // 清空文件选择
  // 清空表单数据
  addOrEditForm.value.id = ""
  addOrEditForm.value.name = ""
  addOrEditForm.value.remark = ""
  addOrEditForm.value.filePath = ""
  addOrEditForm.value.fileSize = ""
  addOrEditForm.value.fileSuffix = ""
  currentItem.value = tableRef.value.getCurrentRecord() // 获取拿到的当前列数据
  fileEl.value.click();
};
// 点击修改范本
const editContractTemplate = async ({ row }: { row: any }) => {
  const { id, name, remark, filePath, classify } = row;

  // 新增字段规则
  // 新增强制条款
  // await addMandatoryTerm({
  //   name,
  //   content: '测试' + dayjs(new Date()).format('YYYY-MM-DD HH:mm:ss'),
  //   contractTemplateId: id
  // })
  // 获取字段规则
  const fieldRuleRes = await getStandardFieldRuleList({
    name: '',
    contractTemplateId: id
  })
  fieldRule.value = fieldRuleRes
  // 获取强制条款
  const mandatoryTermRes = await getMandatoryTermList({
    contractTemplateId: id
  })
  mandatoryTerm.value = mandatoryTermRes



  const blob = await fileDownload(filePath);
  const file = new File([blob], `filePath`, {
    type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  });
  addOrEditForm.value.id = id
  addOrEditForm.value.name = name
  addOrEditForm.value.remark = remark

  addOrEditForm.value.filePath = filePath;

  dcoxFile.value = file
  drawerVisible.value = true;
}
// 获取文件回调
const handleFileChange = async () => {
  const file = fileEl.value.files[0];
  if (!file) return;
  dcoxFile.value = file;

  // 调用获取详情接口
  addOrEditForm.value.classify = currentItem.value.classify
  // const id = ''
  // const fileRes = await getContractTemplateDetail(id);

  drawerVisible.value = true;
};
// 提交按钮
async function submit(data: any) {
  // 新增
  if (addOrEditForm.value.id === '') {
    const file = data.file
    const fileRes = await fileUpload(file);
    // 先触发导入 后触发
    let params = {
      classify: currentItem.value.classify, // 合同范本分类
      name: data.name, // 范本名称
      remark: data.remark, // 范本说明
      filePath: fileRes.fileName, // 文件位置
      fileSize: fileRes.size,
      fileSuffix: fileRes.suffix
    }
    const res = await addContractStandard(params);
    if (res) {
      ElMessage.success('提交成功')
      refreshTable()
    }
    drawerVisible.value = false;
  } else {
    const file = data.file
    const fileRes = await fileUpload(file,);
    // 修改
    let params = {
      name: data.name, // 范本名称
      remark: data.remark, // 范本说明
      filePath: fileRes.fileName, // 文件位置
      fileSize: fileRes.size,
      fileSuffix: fileRes.suffix
    }
    const res = await editContractStandard(addOrEditForm.value.id, params)
    if (res) {
      ElMessage.success('修改成功')
      refreshTable()
    }
    drawerVisible.value = false;
  }
}

// 获取合同范本数据
async function getList() {
  let params = {};
  const res = await getContractStandardList(params);

  tableOptions.data = res;
}

async function refreshTable() {
  await getList();
  tableRef.value.setAllTreeExpand(true)
}

onBeforeMount(async () => {
  await getList();
});
</script>

<style lang="scss">
.vxe-cell--tree-node,
.vxe-tree-cell {
  padding-left: 0 !important;
}
</style>
