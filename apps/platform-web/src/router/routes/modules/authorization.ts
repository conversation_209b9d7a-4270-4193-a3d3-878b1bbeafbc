import type { RouteRecordRaw } from 'vue-router';

// import { $t } from '#/locales';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      icon: 'lucide:layout-dashboard',
      order: 2,
      title: '授权管理',
    },
    name: 'Dashboard',
    path: '/dashboard',
    children: [
      {
        name: 'Author',
        path: '/dashboard/author',
        component: () => import('#/views/authorization/author/index.vue'),
        meta: {
          icon: 'carbon:enterprise',
          title: '授权管理',
        },
        children: [],
      },
    ],
  },
];

export default routes;
