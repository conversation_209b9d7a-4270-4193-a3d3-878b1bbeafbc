<template>
  <div v-if="currOption" class="filter-input">
    <vxe-input
      mode="text"
      v-model="currOption.data"
      placeholder="请输入文本"
      @keyup="keyupEvent"
      @input="changeOptionEvent"
    />
  </div>
</template>

<script lang="ts" setup>
import type { VxeInputEvents } from 'vxe-pc-ui';
import type { VxeTableDefines } from 'vxe-table';

import { computed, ref, watch } from 'vue';

const props = defineProps({
  renderParams: {
    type: Object,
    default: () => ({}),
  },
});

const currOption = ref<VxeTableDefines.FilterOption>();

const currField = computed(() => {
  const { column } = props.renderParams || {};
  return column ? column.field : '';
});

const load = () => {
  const { renderParams } = props;
  if (renderParams) {
    const { column } = renderParams;
    const option = column.filters[0];
    currOption.value = option;
  }
};

const changeOptionEvent = () => {
  const { renderParams } = props;
  const option = currOption.value;
  if (renderParams && option) {
    const { $table } = renderParams;
    const checked = !!option.data;
    $table.updateFilterOptionStatus(option, checked);
  }
};

const keyupEvent: VxeInputEvents.Keyup = ({ $event }) => {
  const { renderParams } = props;
  if (renderParams) {
    const { $table } = renderParams;
    if ($event.key === 'Enter') {
      $table.saveFilterPanel();
    }
  }
};

watch(currField, () => {
  load();
});

load();
</script>

<style scoped>
.filter-input {
  padding: 10px;
}
</style>
